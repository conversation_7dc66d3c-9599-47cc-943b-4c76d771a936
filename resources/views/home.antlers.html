<div class="hero">
    <div class="hero-content"  style="background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449), url('{{ glide:brand:header_img w="1080" h="720" fit="crop" q="80" }}'); background-size: cover;">
        <div class="banner">
            <h1>Blod, Svett & Barr</h1>
            <span>Vi genomför 99% av alla våra aktiviteter ute i friska luften. Välkomna till OK Tyr, Värmlands största orienterings och cykelklubb med hemmaplan i Karlstad och Hammarö.</span>
            <div class="hero-cta">
                <a class="button button-secondary" href="/kalender">Aktivitetskalendern</a>
                <a class="button button-primary" href="/bli-medlem">Bli medlem</a>
            </div>
        </div>
    </div>
</div>

    <div class="container">
        <section class="highlighted-updates">
            <div class="highlighted-updates-news">
                {{ collection:news limit="1" }}
                        <article>
                            <a href="{{ url }}">
                                <div>
                                    {{ if featured_image }}
                                        <img src="{{ glide:featured_image w="1080" h="720" fit="crop" q="80" }}" alt="{{ featured_image:alt ?? title }}">
                                    {{ else }}
                                        <div></div>
                                    {{ /if }}
                                    <div class="highlighted-news-content">
                                        <div>
                                            <time datetime="{{ date format='c' }}">
                                                {{ date format="j M Y" }}
                                            </time>
                                            <h2>{{ title }}</h2>
                                            {{ if lead }}
                                                <p>{{ lead | truncate:200 }}...</p>
                                            {{ /if }}
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </article>
                    {{ /collection:news }}
            </div>
            <div class="highlighted-updates-calendar">
                <div>
                    <h3>Kommande evenemang</h3>
                    {{# <a href="/kalender">Visa alla</a> #}}
                </div>
                <div>
                    {{ autocal:events eventdays="5" group_by_date="true" }}
                        {{ if is_multiple }}
                            <!-- Multiple events on same day - show with stack effect -->
                            <a href="{{ calendar_url }}">
                                <div class="multiple-events">
                                    <span>{{ date_formatted format="j" }}</span>
                                    <span>{{ date_formatted format="M" }}</span>
                                </div>
                                <div>
                                    <h4>{{ display_title }}</h4>
                                    <div>
                                        <span>{{ display_time }}</span>
                                        <span>{{ display_location }}</span>
                                    </div>
                                </div>
                            </a>
                        {{ else }}
                            <!-- Single event - show normally -->
                            <a href="{{ single_event:event_url }}">
                                <div>
                                    <span>{{ date_formatted format="j" }}</span>
                                    <span>{{ date_formatted format="M" }}</span>
                                </div>
                                <div>
                                    <h4>{{ single_event:title }}</h4>
                                    <div>
                                        <span>{{ single_event:start_date format="H:i" }}</span>
                                        {{ if single_event:location }}
                                            <span>{{ single_event:location | truncate:25 }}</span>
                                        {{ /if }}
                                    </div>
                                </div>
                            </a>
                        {{ /if }}
                    {{ /autocal:events }}
                </div>

                <div>
                    <a href="/kalender">
                        Se fullständig kalender →
                    </a>
                </div>
            </div>
        </section>
    </div>
<!-- Latest News Carousel -->
<section class="news-carousel">
    <div class="container">
        <div class="carousel-wrapper">
            <div class="carousel-scroll" id="news-carousel">
                {{ collection:news offset="1" limit="8" }}
                    <article class="carousel-card">
                        <a href="{{ url }}">
                            <div class="card-image">
                                {{ if featured_image }}
                                    <img src="{{ glide:featured_image w="320" h="200" fit="crop" q="85" }}" alt="{{ featured_image:alt ?? title }}">
                                {{ else }}
                                    <div class="card-placeholder">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                                        </svg>
                                    </div>
                                {{ /if }}
                            </div>
                            <div class="card-content">
                                <time datetime="{{ date format='c' }}">{{ date format="j M Y" }}</time>
                                <h4>{{ title }}</h4>
                                {{ if lead }}
                                    <p>{{ lead | truncate:100 }}</p>
                                {{ /if }}
                            </div>
                        </a>
                    </article>
                {{ /collection:news }}

                <!-- Archive Card -->
                <article class="carousel-card carousel-card--archive">
                    <a href="/nyheter">
                        <div class="archive-content">
                            <div class="archive-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                </svg>
                            </div>
                            <h4>Nyhetsarkiv</h4>
                            <p>Utforska alla våra artiklar och rapporter</p>
                            <span class="archive-cta">Visa arkiv →</span>
                        </div>
                    </a>
                </article>
            </div>
        </div>
        <div class="carousel-header">
            <div class="carousel-nav">
                <button class="carousel-btn carousel-btn--prev" aria-label="Föregående nyheter">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
                    </svg>
                </button>
                <button class="carousel-btn carousel-btn--next" aria-label="Nästa nyheter">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</section>

<section>
    {{ content }}
</section>
