// Basic reset
* { box-sizing: border-box; }

// Base typography setup
html {
    font-size: 16px; // Base font size
    line-height: 1.5;
}

body {
    margin: 0;
    padding: 8px;
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.6;
    color: #1a1a1a;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #eef2f6;
}
.container {
    max-width: 1440px;
    margin: 40px auto;
}

// Inter Typography Scale
// Based on a 1.25 (Major Third) scale with optical adjustments for Inter

// Headings
h1 {
    font-size: 2.25rem;   // 36px
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: -0.01em;
    margin: 0 0 1.5rem 0;
}

h2 {
    font-size: 1.875rem;  // 30px
    line-height: 1.25;
    font-weight: 600;
    letter-spacing: -0.01em;
    margin: 0 0 1.25rem 0;
}

h3 {
    font-size: 1.5rem;    // 24px
    line-height: 1.3;
    font-weight: 600;
    letter-spacing: -0.005em;
    margin: 0 0 1rem 0;
}

h4 {
    font-size: 1.25rem;   // 20px
    line-height: 1.35;
    font-weight: 600;
    margin: 0 0 1rem 0;
}

h5 {
    font-size: 1.125rem;  // 18px
    line-height: 1.4;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
}

h6 {
    font-size: 1rem;      // 16px
    line-height: 1.45;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
}

// Body text
p {
    font-size: 1rem;      // 16px
    line-height: 1.6;
    font-weight: 400;
    margin: 0 0 1rem 0;
}

// Remove margin from last child
h1, h2, h3, h4, h5, h6, p {
    &:last-child {
        margin-bottom: 0;
    }
}

// Responsive typography adjustments
@media (max-width: 768px) {
    h1 {
        font-size: 1.875rem;  // 30px on mobile
        line-height: 1.2;
    }

    h2 {
        font-size: 1.5rem;    // 24px on mobile
        line-height: 1.25;
    }

    h3 {
        font-size: 1.25rem;   // 20px on mobile
        line-height: 1.3;
    }

    h4 {
        font-size: 1.125rem;  // 18px on mobile
        line-height: 1.35;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.5rem;    // 24px on small mobile
        line-height: 1.25;
    }

    h2 {
        font-size: 1.25rem;   // 20px on small mobile
        line-height: 1.3;
    }

    h3 {
        font-size: 1.125rem;  // 18px on small mobile
        line-height: 1.35;
    }
}

// Button System
// Base button styles
button, .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 20rem;
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(0) scale(1);

    // Remove default button styles
    background: none;
    outline: none;

    // Subtle hover animation
    &:hover {
        transform: translateY(-1px) scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    // Subtle click effect
    &:active {
        transform: translateY(0) scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.1s ease;
    }

    &:focus {
        outline: 2px solid rgba(1, 100, 73, 0.5);
        outline-offset: 2px;
    }
}

// Primary button (#9C2B32 background with white text)
.button-primary, button.primary {
    background-color: #9C2B32;
    color: white;

    &:hover {
        background-color: #cfe7cb;
        color: #016449;
        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);
    }
}

// Secondary button (#016449 with white text)
.button-secondary, button.secondary {
    background-color: #016449;
    color: white;

    &:hover {
        background-color: #cfe7cb;
        color: #016449;
        box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);
    }
}

// Menu CTA button (black background, primary color on hover)
.button-menu, button.menu {
    background-color: #000000;
    color: white;

    &:hover {
        background-color: #9C2B32;
        color: white;
        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);
    }
}

// Button sizes
.button-sm, button.sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.button-lg, button.lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

// Button variants
.button-outline {
    background-color: transparent;
    border: 2px solid #016449;
    color: #016449;
    box-shadow: none;

    &:hover {
        background-color: #016449;
        color: white;
        box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);
        border-color: #016449;
    }

    &:active {
        transform: translateY(0) scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.1s ease;
    }
}

.button-outline-primary {
    background-color: transparent;
    border: 2px solid #9C2B32;
    color: #9C2B32;
    box-shadow: none;

    &:hover {
        background-color: #9C2B32;
        color: white;
        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);
        border-color: #9C2B32;
    }
}

// Disabled state
button:disabled, .button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;

    &:hover {
        background-color: initial;
        color: initial;
    }
}

// Full width button
.button-full, button.full {
    width: 100%;
}

// Button with icon spacing
.button-icon, button.icon {
    gap: 0.5rem;
}

