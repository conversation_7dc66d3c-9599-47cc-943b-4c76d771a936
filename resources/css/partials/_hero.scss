/* ========================================
   Hero Section - Main landing area
   ======================================== */

.hero {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    /* Hero Content Container */
    &-content {
        height: 90vh;
        min-height: 600px;
        max-width: 1440px;
        width: 100%;
        margin: 8px;
        position: relative;
        overflow: hidden;
        border-radius: 24px;
        padding: 2rem;

        /* Layout */
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;

        /* Background & Visual Effects */
        background-size: cover;
        background-position: center center !important;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%),
                    linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                    linear-gradient(0deg, #016449, #016449);
        background-blend-mode: normal, normal, color, normal, normal;

        /* Typography */
        color: white;
    }

    /* Call-to-Action Section */
    &-cta {
        margin-top: 2rem;
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
}

/* ========================================
   Banner Content Area
   ======================================== */

.banner {
    width: 50vw;
    padding: 2rem;

    /* Typography Styles */
    h1 {
        /* Inherits from global h1 styles */
    }

    span {
        /* Lead text styling - inherits from global styles */
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        width: 100%;
        padding: 0;
    }
}

