/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */
.highlighted-updates {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;

    /* Mobile: Stack vertically with calendar first */
    grid-template-columns: 1fr;
    grid-template-areas:
        "calendar"
        "news";

    /* Desktop: Side by side with 2:1 ratio */
    @media (min-width: 768px) {
        grid-template-columns: 2fr 1fr;
        grid-template-areas: "news calendar";
    }

    h2 {
        grid-column: 1 / -1;
        margin-bottom: 1.5rem;
    }
}

/* News Section - Featured article with overlay */
.highlighted-updates-news {
    grid-area: news;

    article {
        position: relative;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;

            > div {
                position: relative;
                min-height: 300px;
                height: 100%;

                @media (min-width: 768px) {
                    min-height: 400px;
                }
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
            }

            /* Fallback for missing images */
            > div > div:first-child:not(.highlighted-news-content) {
                background: linear-gradient(135deg, #016449, #9C2B32);
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
}

/* News content overlay */
.highlighted-news-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;

    time {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    h2 {
        margin: 0;
        font-size: 1.5rem;
        line-height: 1.3;

        @media (min-width: 768px) {
            font-size: 1.875rem;
        }
    }

    p {
        margin: 0.75rem 0 0 0;
        opacity: 0.9;
        line-height: 1.5;
    }
}

/* Calendar Section - Stacked event layout */
.highlighted-updates-calendar {
    grid-area: calendar;

    > div:first-child {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h3 {
            margin: 0;
            color: #016449;
        }

        a {
            color: #9C2B32;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    /* Events container */
    > div:nth-child(2) {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    /* Individual event items */
    a {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: white;
        border-radius: 12px;
        text-decoration: none;
        color: inherit;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        /* Date component (stylized) */
        > div:first-child {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            background: #016449;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;

            span:first-child {
                font-size: 1.25rem;
                font-weight: 700;
                line-height: 1;
            }

            span:last-child {
                font-size: 0.75rem;
                font-weight: 500;
                text-transform: uppercase;
                opacity: 0.9;
            }
        }

        /* Event details */
        > div:last-child {
            flex: 1;
            min-width: 0;

            h4 {
                margin: 0 0 0.25rem 0;
                font-size: 1rem;
                font-weight: 600;
                color: #1a1a1a;
                line-height: 1.3;

                /* Truncate long titles */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            > div {
                display: flex;
                flex-direction: column;
                gap: 0.125rem;

                span {
                    font-size: 0.875rem;
                    color: #666;

                    &:first-child {
                        font-weight: 500;
                        color: #016449;
                    }
                }
            }
        }
    }

    /* "See full calendar" link */
    > div:last-child {
        text-align: center;

        a {
            display: inline-block;
            color: #016449;
            text-decoration: none;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border: 2px solid #016449;
            border-radius: 8px;
            transition: all 0.2s ease;

            &:hover {
                background: #016449;
                color: white;
            }
        }
    }
}