/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */
.highlighted-updates {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;

    /* Mobile: Stack vertically with calendar first */
    grid-template-columns: 1fr;
    grid-template-areas:
        "calendar"
        "news";

    /* Desktop: Side by side with 2:1 ratio */
    @media (min-width: 768px) {
        grid-template-columns: 2fr 1fr;
        grid-template-areas: "news calendar";
    }

    h2 {
        grid-column: 1 / -1;
        margin-bottom: 1.5rem;
    }
}

/* News Section - Featured article with overlay */
.highlighted-updates-news {
    grid-area: news;

    article {
        position: relative;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;

            > div {
                position: relative;
                min-height: 300px;
                height: 100%;

                @media (min-width: 768px) {
                    min-height: 400px;
                }
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
            }

            /* Fallback for missing images */
            > div > div:first-child:not(.highlighted-news-content) {
                background: linear-gradient(135deg, #016449, #9C2B32);
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
}

/* News content overlay */
.highlighted-news-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;

    time {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    h2 {
        margin: 0;
        font-size: 1.5rem;
        line-height: 1.3;

        @media (min-width: 768px) {
            font-size: 1.875rem;
        }
    }

    p {
        margin: 0.75rem 0 0 0;
        opacity: 0.9;
        line-height: 1.5;
    }
}

/* Calendar Section - Stacked event layout */
.highlighted-updates-calendar {
    grid-area: calendar;

    > div:first-child {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h3 {
            margin: 0;
            color: #016449;
        }

        a {
            color: #9C2B32;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    /* Events container - compact single container */
    > div:nth-child(2) {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    /* Individual event items - compact layout */
    a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 0;
        text-decoration: none;
        color: inherit;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s ease;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background: rgba(1, 100, 73, 0.05);
            margin: 0 -1rem;
            padding-left: 1rem;
            padding-right: 1rem;
            border-radius: 8px;
        }

        /* Date component with card-stack effect for multiple events */
        > div:first-child {
            flex-shrink: 0;
            width: 50px;
            height: 50px;
            background: #016449;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;

            /* Card stack effect for multiple events */
            &.multiple-events {
                &::before {
                    content: '';
                    position: absolute;
                    top: -3px;
                    left: -3px;
                    right: 3px;
                    bottom: 3px;
                    background: rgba(1, 100, 73, 0.3);
                    border-radius: 8px;
                    z-index: -1;
                }

                &::after {
                    content: '';
                    position: absolute;
                    top: -6px;
                    left: -6px;
                    right: 6px;
                    bottom: 6px;
                    background: rgba(1, 100, 73, 0.15);
                    border-radius: 8px;
                    z-index: -2;
                }
            }

            span:first-child {
                font-size: 1.125rem;
                font-weight: 700;
                line-height: 1;
            }

            span:last-child {
                font-size: 0.625rem;
                font-weight: 500;
                text-transform: uppercase;
                opacity: 0.9;
            }
        }

        /* Event details */
        > div:last-child {
            flex: 1;
            min-width: 0;

            h4 {
                margin: 0 0 0.125rem 0;
                font-size: 0.875rem;
                font-weight: 600;
                color: #1a1a1a;
                line-height: 1.3;

                /* Truncate long titles */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            > div {
                display: flex;
                gap: 0.5rem;

                span {
                    font-size: 0.75rem;
                    color: #666;

                    &:first-child {
                        font-weight: 500;
                        color: #016449;
                    }
                }
            }
        }
    }

    /* "See full calendar" link - use secondary button */
    > div:last-child {
        text-align: center;

        a {
            /* Use existing secondary button styles */
            @extend .button-secondary;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
    }
}

/* News Carousel Section */
.news-carousel {
    background: white;
    padding: 3rem 0;

    .container {
        max-width: 1440px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Carousel header */
    .carousel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;

        h3 {
            margin: 0;
            color: #016449;
        }

        .carousel-controls {
            display: flex;
            gap: 0.5rem;

            button {
                width: 40px;
                height: 40px;
                border: 2px solid #016449;
                background: transparent;
                color: #016449;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
                transition: all 0.2s ease;

                &:hover:not(:disabled) {
                    background: #016449;
                    color: white;
                }

                &:disabled {
                    opacity: 0.3;
                    cursor: not-allowed;
                }
            }
        }
    }

    /* Carousel container */
    .carousel-container {
        position: relative;
        overflow: hidden;
    }

    /* Carousel track */
    .carousel-track {
        display: flex;
        gap: 1.5rem;
        transition: transform 0.3s ease;
        cursor: grab;

        &:active {
            cursor: grabbing;
        }

        &.dragging {
            transition: none;
        }
    }

    /* News cards */
    .news-card {
        flex: 0 0 300px;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        @media (max-width: 768px) {
            flex: 0 0 280px;
        }

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;
        }

        .card-image {
            height: 180px;
            overflow: hidden;
            background: linear-gradient(135deg, #016449, #9C2B32);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .card-content {
            padding: 1.25rem;

            time {
                display: block;
                font-size: 0.75rem;
                color: #666;
                margin-bottom: 0.5rem;
                font-weight: 500;
            }

            h4 {
                margin: 0 0 0.75rem 0;
                font-size: 1rem;
                font-weight: 600;
                line-height: 1.4;
                color: #1a1a1a;

                /* Limit to 2 lines */
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            p {
                margin: 0;
                font-size: 0.875rem;
                color: #666;
                line-height: 1.5;

                /* Limit to 3 lines */
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
        }
    }

    /* Archive card - special styling */
    .archive-card {
        flex: 0 0 300px;
        background: linear-gradient(135deg, #016449, #9C2B32);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        @media (max-width: 768px) {
            flex: 0 0 280px;
        }

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        a {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            min-height: 300px;
            text-decoration: none;
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .archive-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        p {
            margin: 0 0 1rem 0;
            font-size: 0.875rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .archive-cta {
            font-size: 0.875rem;
            font-weight: 500;
            opacity: 0.8;
        }
    }
}