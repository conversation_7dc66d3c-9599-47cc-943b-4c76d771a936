/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */
.highlighted-updates {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;

    /* Mobile: Stack vertically with calendar first */
    grid-template-columns: 1fr;
    grid-template-areas:
        "calendar"
        "news";

    /* Desktop: Side by side with 2:1 ratio */
    @media (min-width: 768px) {
        grid-template-columns: 2fr 1fr;
        grid-template-areas: "news calendar";
    }

    h2 {
        grid-column: 1 / -1;
        margin-bottom: 1.5rem;
    }
}

/* News Section - Featured article with overlay */
.highlighted-updates-news {
    grid-area: news;

    article {
        position: relative;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;

            > div {
                position: relative;
                min-height: 300px;
                height: 100%;

                @media (min-width: 768px) {
                    min-height: 400px;
                }
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
            }

            /* Fallback for missing images */
            > div > div:first-child:not(.highlighted-news-content) {
                background: #016449;
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
}

/* News content overlay */
.highlighted-news-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;

    time {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    h2 {
        margin: 0;
        font-size: 1.5rem;
        line-height: 1.3;

        @media (min-width: 768px) {
            font-size: 1.875rem;
        }
    }

    p {
        margin: 0.75rem 0 0 0;
        opacity: 0.9;
        line-height: 1.5;
    }
}

/* Calendar Section - Stacked event layout */
.highlighted-updates-calendar {
    grid-area: calendar;

    > div:first-child {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h3 {
            margin: 0;
            color: #016449;
        }

        a {
            color: #9C2B32;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    /* Events container - compact single container */
    > div:nth-child(2) {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    /* Individual event items - compact layout */
    a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 0;
        text-decoration: none;
        color: inherit;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s ease;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background: rgba(1, 100, 73, 0.05);
            margin: 0 -1rem;
            padding-left: 1rem;
            padding-right: 1rem;
            border-radius: 8px;
        }

        /* Date component with card-stack effect for multiple events */
        > div:first-child {
            flex-shrink: 0;
            width: 50px;
            height: 50px;
            background: #016449;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;

            /* Card stack effect for multiple events */
            &.multiple-events {
                &::before {
                    content: '';
                    position: absolute;
                    top: -3px;
                    left: -3px;
                    right: 3px;
                    bottom: 3px;
                    background: rgba(1, 100, 73, 0.3);
                    border-radius: 8px;
                    z-index: -1;
                }

                &::after {
                    content: '';
                    position: absolute;
                    top: -6px;
                    left: -6px;
                    right: 6px;
                    bottom: 6px;
                    background: rgba(1, 100, 73, 0.15);
                    border-radius: 8px;
                    z-index: -2;
                }
            }

            span:first-child {
                font-size: 1.125rem;
                font-weight: 700;
                line-height: 1;
            }

            span:last-child {
                font-size: 0.625rem;
                font-weight: 500;
                text-transform: uppercase;
                opacity: 0.9;
            }
        }

        /* Event details */
        > div:last-child {
            flex: 1;
            min-width: 0;

            h4 {
                margin: 0 0 0.125rem 0;
                font-size: 0.875rem;
                font-weight: 600;
                color: #1a1a1a;
                line-height: 1.3;

                /* Truncate long titles */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            > div {
                display: flex;
                gap: 0.5rem;

                span {
                    font-size: 0.75rem;
                    color: #666;

                    &:first-child {
                        font-weight: 500;
                        color: #016449;
                    }
                }
            }
        }
    }

    /* "See full calendar" link - use secondary button */
    > div:last-child {
        text-align: center;

        a {
            /* Use existing secondary button styles */
            @extend .button-secondary;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
    }
}

/* News Carousel - Mobile-First Implementation */
.news-carousel {
    padding: 3rem 0;

    .container {
        max-width: 1440px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header with navigation */
    .carousel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;

        h3 {
            margin: 0;
            color: #016449;
            font-size: 1.5rem;
        }

        .carousel-nav {
            display: flex;
            gap: 0.5rem;

            @media (max-width: 640px) {
                display: none; /* Hide on mobile, rely on touch scrolling */
            }
        }
    }

    /* Navigation buttons */
    .carousel-btn {
        width: 40px;
        height: 40px;
        border: none;
        background: white;
        color: #1a1a1a;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        flex-shrink: 0;

        &:hover:not(:disabled) {
            background: #016449;
            color: white;
        }

        &:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }

        svg {
            width: 18px;
            height: 18px;
        }
    }

    /* Carousel wrapper */
    .carousel-wrapper {
        position: relative;
        padding: 1rem 0;

        // /* Fade effect on edges */
        // &::before,
        // &::after {
        //     content: '';
        //     position: absolute;
        //     top: 0;
        //     bottom: 0;
        //     width: 40px;
        //     z-index: 2;
        //     pointer-events: none;

        //     @media (max-width: 640px) {
        //         display: none; /* No fade on mobile */
        //     }
        // }

        // &::before {
        //     left: 0;
        //     background: linear-gradient(to right, white, transparent);
        // }

        // &::after {
        //     right: 0;
        //     background: linear-gradient(to left, white, transparent);
        // }
    }

    /* Scrollable container */
    .carousel-scroll {
        display: flex;
        gap: 1.5rem;
        overflow-x: auto;
        overflow-y: hidden;
        scroll-behavior: smooth;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE/Edge */
        padding: 0 1rem;
        margin: 0 -1rem;

        /* Hide scrollbar in WebKit browsers */
        &::-webkit-scrollbar {
            display: none;
        }

        @media (max-width: 640px) {
            gap: 1rem;
            padding: 0 0.5rem;
            margin: 0 -0.5rem;
        }
    }

    /* Card base styles */
    .carousel-card {
        flex: 0 0 320px;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        scroll-snap-align: start;
        transition: all 0.3s ease;

        @media (max-width: 768px) {
            flex: 0 0 280px;
        }

        @media (max-width: 480px) {
            flex: 0 0 260px;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;
        }
    }

    /* Card image */
    .card-image {
        height: 200px;
        overflow: hidden;
        background: #f8f9fa;
        position: relative;

        @media (max-width: 480px) {
            height: 160px;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .card-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #016449;
            color: white;

            svg {
                opacity: 0.6;
            }
        }
    }

    /* Card content */
    .card-content {
        padding: 1.25rem;

        @media (max-width: 480px) {
            padding: 1rem;
        }

        time {
            display: block;
            font-size: 0.75rem;
            color: #666;
            margin-bottom: 0.5rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        h4 {
            margin: 0 0 0.75rem 0;
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.4;
            color: #1a1a1a;

            /* Limit to 2 lines */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        p {
            margin: 0;
            font-size: 0.875rem;
            color: #666;
            line-height: 1.5;

            /* Limit to 3 lines */
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }

    /* Archive card */
    .carousel-card--archive {
        background: #9C2B32;
        color: white;

        .archive-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem 1.5rem;
            height: 100%;
            min-height: 300px;

            @media (max-width: 480px) {
                padding: 1.5rem 1rem;
                min-height: 260px;
            }
        }

        .archive-icon {
            margin-bottom: 1rem;
            opacity: 0.9;

            svg {
                width: 48px;
                height: 48px;
            }
        }

        h4 {
            margin: 0 0 0.75rem 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
        }

        p {
            margin: 0 0 1rem 0;
            font-size: 0.875rem;
            opacity: 0.9;
            line-height: 1.5;
            color: white;
        }

        .archive-cta {
            font-size: 0.875rem;
            font-weight: 500;
            opacity: 0.8;
            color: white;
        }
    }
}