/**
 * News Carousel Component
 * Handles horizontal scrolling, touch/drag interactions, and navigation buttons
 */

export function initNewsCarousel() {
  const track = document.getElementById('carousel-track');
  const prevButton = document.getElementById('carousel-prev');
  const nextButton = document.getElementById('carousel-next');
  
  if (!track || !prevButton || !nextButton) return;

  const cards = track.querySelectorAll('.news-card, .archive-card');
  if (cards.length === 0) return;

  let currentIndex = 0;
  let cardWidth = 0;
  let visibleCards = 0;
  let isDragging = false;
  let startX = 0;
  let scrollLeft = 0;

  // Calculate dimensions
  function calculateDimensions() {
    const containerWidth = track.parentElement.offsetWidth;
    const firstCard = cards[0];
    cardWidth = firstCard.offsetWidth;
    const gap = parseInt(getComputedStyle(track).gap) || 24;
    
    // Calculate how many cards are visible
    visibleCards = Math.floor(containerWidth / (cardWidth + gap));
    
    // Ensure at least 1 card is visible
    if (visibleCards < 1) visibleCards = 1;
    
    updateButtons();
  }

  // Update button states
  function updateButtons() {
    prevButton.disabled = currentIndex <= 0;
    nextButton.disabled = currentIndex >= cards.length - visibleCards;
  }

  // Scroll to specific index
  function scrollToIndex(index) {
    const gap = parseInt(getComputedStyle(track).gap) || 24;
    const scrollAmount = index * (cardWidth + gap);
    track.style.transform = `translateX(-${scrollAmount}px)`;
    currentIndex = index;
    updateButtons();
  }

  // Previous button handler
  function goToPrevious() {
    if (currentIndex > 0) {
      scrollToIndex(currentIndex - 1);
    }
  }

  // Next button handler
  function goToNext() {
    const maxIndex = cards.length - visibleCards;
    if (currentIndex < maxIndex) {
      // Calculate how many cards to scroll
      const remainingCards = cards.length - currentIndex - visibleCards;
      const scrollAmount = remainingCards < visibleCards - 1 ? remainingCards : visibleCards - 1;
      scrollToIndex(Math.min(currentIndex + scrollAmount, maxIndex));
    }
  }

  // Touch/Mouse drag handlers
  function handleDragStart(e) {
    isDragging = true;
    track.classList.add('dragging');
    startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
    scrollLeft = currentIndex * (cardWidth + parseInt(getComputedStyle(track).gap) || 24);
  }

  function handleDragMove(e) {
    if (!isDragging) return;
    e.preventDefault();
    
    const currentX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
    const deltaX = startX - currentX;
    const newScrollLeft = scrollLeft + deltaX;
    
    // Apply transform directly for smooth dragging
    track.style.transform = `translateX(-${newScrollLeft}px)`;
  }

  function handleDragEnd(e) {
    if (!isDragging) return;
    isDragging = false;
    track.classList.remove('dragging');
    
    const currentX = e.type === 'mouseup' ? e.clientX : e.changedTouches[0].clientX;
    const deltaX = startX - currentX;
    const gap = parseInt(getComputedStyle(track).gap) || 24;
    
    // Calculate new index based on drag distance
    const dragThreshold = cardWidth / 3; // 1/3 of card width to trigger scroll
    
    if (Math.abs(deltaX) > dragThreshold) {
      if (deltaX > 0 && currentIndex < cards.length - visibleCards) {
        // Dragged left, go to next
        goToNext();
      } else if (deltaX < 0 && currentIndex > 0) {
        // Dragged right, go to previous
        goToPrevious();
      } else {
        // Snap back to current position
        scrollToIndex(currentIndex);
      }
    } else {
      // Snap back to current position
      scrollToIndex(currentIndex);
    }
  }

  // Horizontal scroll handler
  function handleWheel(e) {
    if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
      e.preventDefault();
      
      if (e.deltaX > 0 && currentIndex < cards.length - visibleCards) {
        goToNext();
      } else if (e.deltaX < 0 && currentIndex > 0) {
        goToPrevious();
      }
    }
  }

  // Event listeners
  prevButton.addEventListener('click', goToPrevious);
  nextButton.addEventListener('click', goToNext);

  // Mouse events
  track.addEventListener('mousedown', handleDragStart);
  document.addEventListener('mousemove', handleDragMove);
  document.addEventListener('mouseup', handleDragEnd);

  // Touch events
  track.addEventListener('touchstart', handleDragStart, { passive: false });
  track.addEventListener('touchmove', handleDragMove, { passive: false });
  track.addEventListener('touchend', handleDragEnd);

  // Wheel event for horizontal scrolling
  track.addEventListener('wheel', handleWheel, { passive: false });

  // Prevent context menu on long press
  track.addEventListener('contextmenu', e => e.preventDefault());

  // Resize handler
  window.addEventListener('resize', () => {
    calculateDimensions();
    scrollToIndex(Math.min(currentIndex, cards.length - visibleCards));
  });

  // Initialize
  calculateDimensions();
  scrollToIndex(0);
}
