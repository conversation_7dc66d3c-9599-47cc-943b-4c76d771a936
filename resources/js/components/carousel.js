/**
 * Modern News Carousel - Mobile-First Implementation
 * Uses native scrolling with scroll-snap for optimal mobile performance
 */

export function initNewsCarousel() {
  const carousel = document.getElementById('news-carousel');
  const prevBtn = document.querySelector('.carousel-btn--prev');
  const nextBtn = document.querySelector('.carousel-btn--next');

  if (!carousel || !prevBtn || !nextBtn) return;

  const cards = carousel.querySelectorAll('.carousel-card');
  if (cards.length === 0) return;

  let cardWidth = 0;
  let gap = 0;
  let visibleCards = 1;

  // Calculate dimensions and visible cards
  function calculateDimensions() {
    const firstCard = cards[0];
    if (!firstCard) return;

    cardWidth = firstCard.offsetWidth;
    const computedStyle = getComputedStyle(carousel);
    gap = parseInt(computedStyle.gap) || 24;

    // Calculate visible cards based on container width
    const containerWidth = carousel.parentElement.offsetWidth;
    visibleCards = Math.floor(containerWidth / (cardWidth + gap));

    // Ensure at least 1 card is visible
    if (visibleCards < 1) visibleCards = 1;

    updateButtonStates();
  }

  // Update navigation button states
  function updateButtonStates() {
    const scrollLeft = carousel.scrollLeft;
    const maxScroll = carousel.scrollWidth - carousel.clientWidth;

    prevBtn.disabled = scrollLeft <= 0;
    nextBtn.disabled = scrollLeft >= maxScroll - 1; // -1 for rounding errors
  }

  // Smooth scroll to position
  function scrollToPosition(position) {
    carousel.scrollTo({
      left: position,
      behavior: 'smooth'
    });
  }

  // Navigate to previous cards
  function goToPrevious() {
    const scrollAmount = (cardWidth + gap) * Math.min(visibleCards, 2);
    const newPosition = Math.max(0, carousel.scrollLeft - scrollAmount);
    scrollToPosition(newPosition);
  }

  // Navigate to next cards
  function goToNext() {
    const scrollAmount = (cardWidth + gap) * Math.min(visibleCards, 2);
    const maxScroll = carousel.scrollWidth - carousel.clientWidth;
    const newPosition = Math.min(maxScroll, carousel.scrollLeft + scrollAmount);
    scrollToPosition(newPosition);
  }

  // Handle keyboard navigation
  function handleKeydown(e) {
    if (e.target.closest('.news-carousel')) {
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          goToPrevious();
          break;
        case 'ArrowRight':
          e.preventDefault();
          goToNext();
          break;
      }
    }
  }

  // Event listeners
  prevBtn.addEventListener('click', goToPrevious);
  nextBtn.addEventListener('click', goToNext);

  // Update button states on scroll
  carousel.addEventListener('scroll', updateButtonStates, { passive: true });

  // Keyboard navigation
  document.addEventListener('keydown', handleKeydown);

  // Recalculate on resize
  window.addEventListener('resize', () => {
    calculateDimensions();
    // Small delay to ensure layout is complete
    setTimeout(updateButtonStates, 100);
  });

  // Initialize
  calculateDimensions();

  // Initial button state update after a short delay
  setTimeout(updateButtonStates, 100);
}
