/**
 * Main Site JavaScript
 * Minimal setup for OK Tyr website
 */

import { initScrollNavigation, initDropdownNavigation, initMobileNavigation } from './components/nav.js';
import { initNewsCarousel } from './components/carousel.js';

// Simple DOM ready function
function ready(callback) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', callback);
  } else {
    callback();
  }
}

// Initialize when DOM is ready
ready(() => {
  console.log('OK Tyr website loaded');

  // Initialize scroll navigation
  initScrollNavigation();

  // Initialize dropdown navigation
  initDropdownNavigation();

  // Initialize mobile navigation
  initMobileNavigation();

  // Initialize news carousel
  initNewsCarousel();
});
