{"version": 3, "file": "css/style.css", "mappings": "AACA;EAAI;ACCJ;;ADEA;EACI;EACA;ACCJ;;ADEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ACCJ;;ADCA;EACI;EACA;ACEJ;;ADKA;EACI;EACA;EACA;EACA;EACA;ACFJ;;ADKA;EACI;EACA;EACA;EACA;EACA;ACFJ;;ADKA;EACI;EACA;EACA;EACA;EACA;ACFJ;;ADKA;EACI;EACA;EACA;EACA;ACFJ;;ADKA;EACI;EACA;EACA;EACA;ACFJ;;ADKA;EACI;EACA;EACA;EACA;ACFJ;;ADMA;EACI;EACA;EACA;EACA;ACHJ;;ADQI;EACI;ACLR;;ADUA;EACI;IACI;IACA;ECPN;EDUE;IACI;IACA;ECRN;EDWE;IACI;IACA;ECTN;EDYE;IACI;IACA;ECVN;AACF;ADaA;EACI;IACI;IACA;ECXN;EDcE;IACI;IACA;ECZN;EDeE;IACI;IACA;ECbN;AACF;ADkBA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;AClBJ;ADqBI;EACI;EACA;ACnBR;ADuBI;EACI;EACA;EACA;ACrBR;ADwBI;EACI;EACA;ACtBR;;AD2BA;EACI;EACA;ACxBJ;AD0BI;EACI;EACA;EACA;ACxBR;;AD6BA;EACI;EACA;AC1BJ;AD4BI;EACI;EACA;EACA;AC1BR;;AD+BA;EACI;EACA;AC5BJ;AD8BI;EACI;EACA;EACA;AC5BR;;ADiCA;EACI;EACA;AC9BJ;;ADiCA;EACI;EACA;AC9BJ;;ADkCA;EACI;EACA;EACA;EACA;AC/BJ;ADiCI;EACI;EACA;EACA;EACA;AC/BR;ADkCI;EACI;EACA;EACA;AChCR;;ADoCA;EACI;EACA;EACA;EACA;ACjCJ;ADmCI;EACI;EACA;EACA;EACA;ACjCR;;ADsCA;EACI;EACA;EACA;ACnCJ;ADqCI;EACI;EACA;ACnCR;;ADwCA;EACI;ACrCJ;;ADyCA;EACI;ACtCJ;;AC1OA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;AD2OJ;ACxOI;EACI;AD0OR;ACtOI;EACI;ADwOR;;ACpOA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;ADqOJ;AClOI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADoOR;ACjOQ;;EAEI;EACA;ADmOZ;AC/NQ;EACI;EACA;ADiOZ;AC9NQ;EACI;EACA;ADgOZ;AC5NQ;EACI;EACA;EACA;EACA;EACA;EACA;AD8NZ;AC1NQ;EACI;AD4NZ;;ACtNA;EACI;IAEI;IACA;IAGA;IACA;IAGA;IACA;EDsNN;ECnNM;IACI;IACA;IACA;EDqNV;AACF;AC/MA;EAEI;EACA;EACA;ADgNJ;;AC7MA;EACI;EACA;EACA;KAAA;ADgNJ;;AC7MA;EAEI;EACA;EACA;EACA;AD+MJ;;AC5MA;EACI;AD+MJ;;AC5MA;EAEI;AD8MJ;;AC3MA;EACI;AD8MJ;;AC1MA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;AD2MJ;ACzMI;;EACI;EAEA;EACA;AD2MR;ACxMI;;EAEI;EACA;AD0MR;ACvMI;;EACI;EACA;AD0MR;;ACnMI;;EACI;EACA;ADuMR;AClMQ;;EACI;EACA;ADqMZ;ACjMQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADoMZ;AC/LI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADkMR;;AC7LA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADgMJ;AC7LI;EAjBJ;IAkBQ;IACA;EDgMN;AACF;AC7LI;EAEI;EACA;EACA;AD8LR;;ACzLA;EACI;EACA;EACA;EACA;AD4LJ;;ACxLA;EACI;AD2LJ;;ACxLA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;ADyLJ;ACvLI;;EACI;EAEA;EACA;ADyLR;ACtLI;;EAEI;EACA;ADwLR;ACrLI;;EACI;EACA;EACA;ADwLR;;ACpLA;EAEI;EACA;ADsLJ;;AClLA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;ADmLJ;ACjLI;EACI;EAEA;EACA;ADkLR;AC/KI;EAEI;EACA;ADgLR;AC7KI;EACI;EACA;AD+KR;;AC3KA;EACI;EACA;EACA;EACA;EACA;EACA;AD8KJ;;AC3KA;EACI;EACA;EACA;EACA;EACA;AD8KJ;;AC3KA;EACI;EACA;EACA;AD8KJ;;ACxKQ;EACI;EACA;AD2KZ;ACzKQ;EAEI;AD0KZ;ACxKQ;EACI;EACA;AD0KZ;;ACpKA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;ADuKJ;ACpKI;EACI;EACA;EACA;ADsKR;;AClKA;EACI;EACA;ADqKJ;;ACjKA;EACI;EACA;EACA;EACA;EACA;ADoKJ;ACjKI;EACI;ADmKR;AChKQ;EAAiB;ADmKzB;AClKQ;EAAiB;ADqKzB;ACpKQ;EAAiB;ADuKzB;ACtKQ;EAAiB;ADyKzB;ACxKQ;EAAiB;AD2KzB;AC1KQ;EAAiB;AD6KzB;AC5KQ;EAAiB;AD+KzB;AC9KQ;EAAiB;ADiLzB;;AC7KA;EACI;IACI;IACA;EDgLN;AACF;AC5KA;;EAEI;EACA;EACA;AD8KJ;;AC3KA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;AD8KJ;AC5KI;;EACI;AD+KR;AC5KI;;EACI;EACA;AD+KR;;AC1KA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;AD2KJ;ACzKI;;EACI;EAEA;EACA;AD2KR;ACxKI;;EAEI;EACA;AD0KR;ACvKI;;EACI;EACA;AD0KR;;ACnKI;;EACI;EACA;ADuKR;AClKQ;;EACI;EACA;ADqKZ;ACjKQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADoKZ;AC/JI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADkKR;;AC5JA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD+JJ;AC7JI;EACI;AD+JR;AC5JI;EACI;EACA;AD8JR;;AC1JA;EACI;EACA;AD6JJ;;AC1JA;;EAEI;EACA;EACA;AD6JJ;;AC1JA;EACI;EACA;EACA;AD6JJ;;ACzJA;;EAEI;EACA;EACA;AD4JJ;AC1JI;;EACI;AD6JR;;ACzJA;EACI;AD4JJ;;ACxJA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AD2JJ;ACxJI;EACI;AD0JR;;ACtJA;EAGI;EACA;EACA;EACA;ADuJJ;;ACnJA;EACI;IACI;EDsJN;AACF;AClJA;EACI;IACI;EDoJN;AACF;AChJA;EACI;IACI;EDkJN;EC/IE;IACI;EDiJN;EC7IE;IACI;ED+IN;EC7IM;IACI;ED+IV;EC3IM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;ED6IV;ECvIM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;EDyIV;AACF;ACrIA;EACI;IACI;EDuIN;ECpIE;IACI;EDsIN;AACF;AClIA;EAGQ;IACI;IACA;IACA;EDkIV;EC9HM;IACI;IACA;IACA;EDgIV;AACF;AC3HA;EAEI;EACA;EACA;AD4HJ;AC1HI;EACI;AD4HR;;ACvHA;;;EAGI;EACA;EACA;AD0HJ;;AE13BA;EACI;EACA;EACA;EACA;AF63BJ;;AEz3BA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AF43BJ;;AEz3BA;EACI;EACA;AF43BJ;;AEz3BA;EACI;EACA;EACA;EACA;AF43BJ;;AEz3BA;EACI;IACI;IACA;EF43BN;AACF;AG16BA;AACA;EACI;EACA;EACA;EAEA;EACA;EACA,sCACI;EAGJ;AHw6BJ;AGv6BI;EAZJ;IAaQ;IACA;EH06BN;AACF;AGx6BI;EACI;EACA;AH06BR;;AGt6BA;AACA;EACI;AHy6BJ;AGv6BI;EACI;EACA;EACA;EACA;EACA;EACA;AHy6BR;AGv6BQ;EACI;EACA;AHy6BZ;AGt6BQ;EACI;EACA;EACA;EACA;EAqBA;AHo5BZ;AGv6BY;EACI;EACA;EACA;AHy6BhB;AGv6BgB;EALJ;IAMQ;EH06BlB;AACF;AGv6BY;EACI;EACA;EACA;KAAA;EACA;EACA;EACA;AHy6BhB;AGr6BY;EACI;EACA;EACA;EACA;EACA;EACA;AHu6BhB;;AGj6BA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AHo6BJ;AGl6BI;EACI;EACA;EACA;EACA;EACA;AHo6BR;AGj6BI;EACI;EACA;EACA;AHm6BR;AGj6BQ;EALJ;IAMQ;EHo6BV;AACF;AGj6BI;EACI;EACA;EACA;AHm6BR;;AG/5BA;AACA;EACI;EAyBA;EAQA;EAwBA;EAuDA;EAkCA;AHqxBJ;AGr6BI;EACI;EACA;EACA;EACA;AHu6BR;AGr6BQ;EACI;EACA;AHu6BZ;AGp6BQ;EACI;EACA;EACA;EACA;AHs6BZ;AGp6BY;EACI;AHs6BhB;AGh6BI;EACI;EACA;EACA;EACA;AHk6BR;AG95BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHg6BR;AG95BQ;EACI;AHg6BZ;AG75BQ;EACI;EACA;EACA;EACA;EACA;AH+5BZ;AG15BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;AH25BR;AGz5BY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH25BhB;AGx5BY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH05BhB;AGt5BQ;EACI;EACA;EACA;AHw5BZ;AGr5BQ;EACI;EACA;EACA;EACA;AHu5BZ;AGl5BI;EACI;EACA;AHo5BR;AGl5BQ;EACI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;AHm5BZ;AGh5BQ;EACI;EACA;AHk5BZ;AGh5BY;EACI;EACA;AHk5BhB;AGh5BgB;EACI;EACA;AHk5BpB;AG34BI;EACI;EACA;AH64BR;;AGz4BA;AACA;EACI;EAQA;EAwBA;EAqEA;EAOA;EA0BA;EA+BA;EAgCA;EA8CA;AHiqBJ;AGl5BI;EACI;EACA;EACA;AHo5BR;AGh5BI;EACI;EACA;EACA;EACA;AHk5BR;AGh5BQ;EACI;EACA;EACA;AHk5BZ;AG/4BQ;EACI;EACA;EACA;AHi5BZ;AG/4BY;EALJ;IAMQ;EHk5Bd;AACF;AG74BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EA0BA;EAaA;EAMA;AHo2BR;AG/4BQ;EACI;EACA;EACA;EACA;AHi5BZ;AG94BQ;EACI;EACA;AHg5BZ;AG74BQ;EACI;EACA;AH+4BZ;AG54BQ;EACI;EACA;EACA;EACA;AH84BZ;AG14BQ;EACI;EACA;EACA;EACA;EACA;AH44BZ;AGz4BQ;EACI;AH24BZ;AGv4BQ;EACI;UAAA;EACA;UAAA;AHy4BZ;AGr4BQ;EACI;UAAA;EACA;UAAA;AHu4BZ;AGl4BI;EACI;EACA;EACA;AHo4BR;AGh4BI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;AHi4BR;AGh4BQ;EACI;AHk4BZ;AG/3BQ;EAlBJ;IAmBQ;IACA;IACA;EHk4BV;AACF;AG93BI;EACI;EACA;EACA;EAEA;EACA;EACA;AH+3BR;AG73BQ;EATJ;IAUQ;EHg4BV;AACF;AG93BQ;EAbJ;IAcQ;EHi4BV;AACF;AG/3BQ;EACI;EACA;AHi4BZ;AG93BQ;EACI;EACA;EACA;EACA;AHg4BZ;AG33BI;EACI;EACA;EACA;EACA;AH63BR;AG33BQ;EANJ;IAOQ;EH83BV;AACF;AG53BQ;EACI;EACA;EACA;KAAA;AH83BZ;AG33BQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AH63BZ;AG33BY;EACI;AH63BhB;AGv3BI;EACI;AHy3BR;AGv3BQ;EAHJ;IAIQ;EH03BV;AACF;AGx3BQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AH03BZ;AGv3BQ;EACI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;AHw3BZ;AGr3BQ;EACI;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;AHs3BZ;AGj3BI;EACI;EACA;AHm3BR;AGj3BQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHm3BZ;AGj3BY;EAVJ;IAWQ;IACA;EHo3Bd;AACF;AGj3BQ;EACI;EACA;AHm3BZ;AGj3BY;EACI;EACA;AHm3BhB;AG/2BQ;EACI;EACA;EACA;EACA;AHi3BZ;AG92BQ;EACI;EACA;EACA;EACA;EACA;AHg3BZ;AG72BQ;EACI;EACA;EACA;EACA;AH+2BZ,C", "sources": ["webpack:///./resources/css/partials/_basics.scss", "webpack:///./resources/css/style.scss", "webpack:///./resources/css/partials/_nav.scss", "webpack:///./resources/css/partials/_hero.scss", "webpack:///./resources/css/partials/_highlighted-updates.scss"], "sourcesContent": ["// Basic reset\n* { box-sizing: border-box; }\n\n// Base typography setup\nhtml {\n    font-size: 16px; // Base font size\n    line-height: 1.5;\n}\n\nbody {\n    margin: 0;\n    padding: 8px;\n    font-family: 'Inter', sans-serif;\n    font-weight: 400;\n    font-size: 1rem;\n    line-height: 1.6;\n    color: #1a1a1a;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background-color: #eef2f6;\n}\n.container {\n    max-width: 1440px;\n    margin: 40px auto;\n}\n\n// Inter Typography Scale\n// Based on a 1.25 (Major Third) scale with optical adjustments for Inter\n\n// Headings\nh1 {\n    font-size: 2.25rem;   // 36px\n    line-height: 1.2;\n    font-weight: 700;\n    letter-spacing: -0.01em;\n    margin: 0 0 1.5rem 0;\n}\n\nh2 {\n    font-size: 1.875rem;  // 30px\n    line-height: 1.25;\n    font-weight: 600;\n    letter-spacing: -0.01em;\n    margin: 0 0 1.25rem 0;\n}\n\nh3 {\n    font-size: 1.5rem;    // 24px\n    line-height: 1.3;\n    font-weight: 600;\n    letter-spacing: -0.005em;\n    margin: 0 0 1rem 0;\n}\n\nh4 {\n    font-size: 1.25rem;   // 20px\n    line-height: 1.35;\n    font-weight: 600;\n    margin: 0 0 1rem 0;\n}\n\nh5 {\n    font-size: 1.125rem;  // 18px\n    line-height: 1.4;\n    font-weight: 600;\n    margin: 0 0 0.75rem 0;\n}\n\nh6 {\n    font-size: 1rem;      // 16px\n    line-height: 1.45;\n    font-weight: 600;\n    margin: 0 0 0.75rem 0;\n}\n\n// Body text\np {\n    font-size: 1rem;      // 16px\n    line-height: 1.6;\n    font-weight: 400;\n    margin: 0 0 1rem 0;\n}\n\n// Remove margin from last child\nh1, h2, h3, h4, h5, h6, p {\n    &:last-child {\n        margin-bottom: 0;\n    }\n}\n\n// Responsive typography adjustments\n@media (max-width: 768px) {\n    h1 {\n        font-size: 1.875rem;  // 30px on mobile\n        line-height: 1.2;\n    }\n\n    h2 {\n        font-size: 1.5rem;    // 24px on mobile\n        line-height: 1.25;\n    }\n\n    h3 {\n        font-size: 1.25rem;   // 20px on mobile\n        line-height: 1.3;\n    }\n\n    h4 {\n        font-size: 1.125rem;  // 18px on mobile\n        line-height: 1.35;\n    }\n}\n\n@media (max-width: 480px) {\n    h1 {\n        font-size: 1.5rem;    // 24px on small mobile\n        line-height: 1.25;\n    }\n\n    h2 {\n        font-size: 1.25rem;   // 20px on small mobile\n        line-height: 1.3;\n    }\n\n    h3 {\n        font-size: 1.125rem;  // 18px on small mobile\n        line-height: 1.35;\n    }\n}\n\n// Button System\n// Base button styles\nbutton, .button {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0.75rem 1.5rem;\n    border: none;\n    border-radius: 20rem;\n    font-family: 'Inter', sans-serif;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    transform: translateY(0) scale(1);\n\n    // Remove default button styles\n    background: none;\n    outline: none;\n\n    // Subtle hover animation\n    &:hover {\n        transform: translateY(-1px) scale(1.02);\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n    }\n\n    // Subtle click effect\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n        transition: all 0.1s ease;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(1, 100, 73, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Primary button (#9C2B32 background with white text)\n.button-primary, button.primary {\n    background-color: #9C2B32;\n    color: white;\n\n    &:hover {\n        background-color: #cfe7cb;\n        color: #016449;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n    }\n}\n\n// Secondary button (#016449 with white text)\n.button-secondary, button.secondary {\n    background-color: #016449;\n    color: white;\n\n    &:hover {\n        background-color: #cfe7cb;\n        color: #016449;\n        box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n    }\n}\n\n// Menu CTA button (black background, primary color on hover)\n.button-menu, button.menu {\n    background-color: #000000;\n    color: white;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);\n    }\n}\n\n// Button sizes\n.button-sm, button.sm {\n    padding: 0.5rem 1rem;\n    font-size: 0.875rem;\n}\n\n.button-lg, button.lg {\n    padding: 1rem 2rem;\n    font-size: 1.125rem;\n}\n\n// Button variants\n.button-outline {\n    background-color: transparent;\n    border: 2px solid #016449;\n    color: #016449;\n    box-shadow: none;\n\n    &:hover {\n        background-color: #016449;\n        color: white;\n        box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n        border-color: #016449;\n    }\n\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n        transition: all 0.1s ease;\n    }\n}\n\n.button-outline-primary {\n    background-color: transparent;\n    border: 2px solid #9C2B32;\n    color: #9C2B32;\n    box-shadow: none;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n        border-color: #9C2B32;\n    }\n}\n\n// Disabled state\nbutton:disabled, .button.disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n\n    &:hover {\n        background-color: initial;\n        color: initial;\n    }\n}\n\n// Full width button\n.button-full, button.full {\n    width: 100%;\n}\n\n// Button with icon spacing\n.button-icon, button.icon {\n    gap: 0.5rem;\n}\n\n", "* {\n  box-sizing: border-box;\n}\n\nhtml {\n  font-size: 16px;\n  line-height: 1.5;\n}\n\nbody {\n  margin: 0;\n  padding: 8px;\n  font-family: \"Inter\", sans-serif;\n  font-weight: 400;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: #1a1a1a;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #eef2f6;\n}\n\n.container {\n  max-width: 1440px;\n  margin: 40px auto;\n}\n\nh1 {\n  font-size: 2.25rem;\n  line-height: 1.2;\n  font-weight: 700;\n  letter-spacing: -0.01em;\n  margin: 0 0 1.5rem 0;\n}\n\nh2 {\n  font-size: 1.875rem;\n  line-height: 1.25;\n  font-weight: 600;\n  letter-spacing: -0.01em;\n  margin: 0 0 1.25rem 0;\n}\n\nh3 {\n  font-size: 1.5rem;\n  line-height: 1.3;\n  font-weight: 600;\n  letter-spacing: -0.005em;\n  margin: 0 0 1rem 0;\n}\n\nh4 {\n  font-size: 1.25rem;\n  line-height: 1.35;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n}\n\nh5 {\n  font-size: 1.125rem;\n  line-height: 1.4;\n  font-weight: 600;\n  margin: 0 0 0.75rem 0;\n}\n\nh6 {\n  font-size: 1rem;\n  line-height: 1.45;\n  font-weight: 600;\n  margin: 0 0 0.75rem 0;\n}\n\np {\n  font-size: 1rem;\n  line-height: 1.6;\n  font-weight: 400;\n  margin: 0 0 1rem 0;\n}\n\nh1:last-child, h2:last-child, h3:last-child, h4:last-child, h5:last-child, h6:last-child, p:last-child {\n  margin-bottom: 0;\n}\n\n@media (max-width: 768px) {\n  h1 {\n    font-size: 1.875rem;\n    line-height: 1.2;\n  }\n  h2 {\n    font-size: 1.5rem;\n    line-height: 1.25;\n  }\n  h3 {\n    font-size: 1.25rem;\n    line-height: 1.3;\n  }\n  h4 {\n    font-size: 1.125rem;\n    line-height: 1.35;\n  }\n}\n@media (max-width: 480px) {\n  h1 {\n    font-size: 1.5rem;\n    line-height: 1.25;\n  }\n  h2 {\n    font-size: 1.25rem;\n    line-height: 1.3;\n  }\n  h3 {\n    font-size: 1.125rem;\n    line-height: 1.35;\n  }\n}\nbutton, .button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 20rem;\n  font-family: \"Inter\", sans-serif;\n  font-size: 1rem;\n  font-weight: 500;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  transform: translateY(0) scale(1);\n  background: none;\n  outline: none;\n}\nbutton:hover, .button:hover {\n  transform: translateY(-1px) scale(1.02);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\nbutton:active, .button:active {\n  transform: translateY(0) scale(0.98);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n  transition: all 0.1s ease;\n}\nbutton:focus, .button:focus {\n  outline: 2px solid rgba(1, 100, 73, 0.5);\n  outline-offset: 2px;\n}\n\n.button-primary, button.primary {\n  background-color: #9C2B32;\n  color: white;\n}\n.button-primary:hover, button.primary:hover {\n  background-color: #cfe7cb;\n  color: #016449;\n  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n}\n\n.button-secondary, button.secondary {\n  background-color: #016449;\n  color: white;\n}\n.button-secondary:hover, button.secondary:hover {\n  background-color: #cfe7cb;\n  color: #016449;\n  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n}\n\n.button-menu, .nav-mobile-cta-button, .nav-cta a, button.menu {\n  background-color: #000000;\n  color: white;\n}\n.button-menu:hover, .nav-mobile-cta-button:hover, .nav-cta a:hover, button.menu:hover {\n  background-color: #9C2B32;\n  color: white;\n  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);\n}\n\n.button-sm, button.sm {\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n}\n\n.button-lg, button.lg {\n  padding: 1rem 2rem;\n  font-size: 1.125rem;\n}\n\n.button-outline {\n  background-color: transparent;\n  border: 2px solid #016449;\n  color: #016449;\n  box-shadow: none;\n}\n.button-outline:hover {\n  background-color: #016449;\n  color: white;\n  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n  border-color: #016449;\n}\n.button-outline:active {\n  transform: translateY(0) scale(0.98);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n  transition: all 0.1s ease;\n}\n\n.button-outline-primary {\n  background-color: transparent;\n  border: 2px solid #9C2B32;\n  color: #9C2B32;\n  box-shadow: none;\n}\n.button-outline-primary:hover {\n  background-color: #9C2B32;\n  color: white;\n  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n  border-color: #9C2B32;\n}\n\nbutton:disabled, .button.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n}\nbutton:disabled:hover, .button.disabled:hover {\n  background-color: initial;\n  color: initial;\n}\n\n.button-full, .nav-mobile-cta-button, button.full {\n  width: 100%;\n}\n\n.button-icon, button.icon {\n  gap: 0.5rem;\n}\n\n.navbar-container {\n  max-width: 1440px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  padding: 0 80px;\n  position: fixed;\n  top: 30px;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  transform: translateY(0);\n  transition: transform 0.4s ease-in-out;\n}\n.navbar-container.nav-hidden {\n  transform: translateY(-200%);\n}\n.navbar-container.nav-visible {\n  transform: translateY(0);\n}\n\n.navbar {\n  position: relative;\n  width: 100%;\n  max-width: 1250px;\n  display: flex;\n  align-items: center;\n  padding: 16px;\n  border-radius: 10rem;\n  color: white;\n  transition: all 0.4s ease;\n  background: rgba(1, 100, 73, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n.navbar.nav-mobile-expanded {\n  border-radius: 1.5rem;\n  flex-direction: column;\n  align-items: stretch;\n  height: 100vh;\n  max-width: none;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  transform-origin: center top;\n}\n.navbar.nav-mobile-expanded .nav-logo,\n.navbar.nav-mobile-expanded .nav-mobile-toggle {\n  position: absolute;\n  z-index: 10;\n}\n.navbar.nav-mobile-expanded .nav-logo {\n  top: 16px;\n  left: 16px;\n}\n.navbar.nav-mobile-expanded .nav-mobile-toggle {\n  top: 16px;\n  right: 16px;\n}\n.navbar.nav-mobile-expanded .nav-mobile-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  padding: 4rem 1rem 1rem;\n  overflow-y: auto;\n}\n.navbar.nav-mobile-expanded .nav-mobile-items {\n  flex: 1;\n}\n\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n  .navbar {\n    background: rgba(1, 100, 73, 0.6);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    -webkit-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    -moz-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n  }\n  .navbar.nav-mobile-expanded {\n    background: rgba(0, 0, 0, 0.6);\n    -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n    backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n  }\n}\n.nav-logo {\n  flex: 0 0 auto;\n  max-width: 40px;\n  max-height: 40px;\n}\n\n.nav-logo img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n.nav-collection {\n  display: flex;\n  align-items: center;\n  flex: 1 1 auto;\n  justify-content: center;\n}\n\n.navbar button {\n  box-shadow: none;\n}\n\n.nav-cta {\n  flex: 0 0 auto;\n}\n\n.nav-item {\n  position: relative;\n}\n\n.nav-link,\n.nav-toggle {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 500;\n  text-decoration: none;\n  color: inherit;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n  box-shadow: none !important;\n  transform: none !important;\n}\n.nav-link:hover,\n.nav-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-link:active,\n.nav-toggle:active {\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-link:focus,\n.nav-toggle:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-toggle .nav-arrow,\n.nav-toggle-level2 .nav-arrow {\n  transition: all 0.3s ease;\n  opacity: 1;\n}\n.nav-toggle[aria-expanded=true] .nav-arrow,\n.nav-toggle-level2[aria-expanded=true] .nav-arrow {\n  opacity: 0;\n  transform: scale(0.8);\n}\n.nav-toggle[aria-expanded=true]::after,\n.nav-toggle-level2[aria-expanded=true]::after {\n  content: \"\";\n  position: absolute;\n  width: 10px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 1;\n  transform: scale(1);\n  transition: all 0.3s ease 0.1s;\n  right: 0.5rem;\n}\n.nav-toggle::after,\n.nav-toggle-level2::after {\n  content: \"\";\n  position: absolute;\n  width: 10px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all 0.3s ease;\n  right: 0.5rem;\n}\n\n.nav-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  min-width: 200px;\n  background: rgba(0, 0, 0, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  padding: 0.5rem 0;\n  margin-top: 0.5rem;\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-10px);\n  transition: all 0.2s ease;\n  z-index: 1000;\n}\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n  .nav-dropdown {\n    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n  }\n}\n.nav-item:hover .nav-dropdown, .nav-item.nav-active .nav-dropdown {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0);\n}\n\n.nav-dropdown-level2 {\n  top: 0;\n  left: 100%;\n  margin-top: 0;\n  margin-left: 0.5rem;\n}\n\n.nav-dropdown-item {\n  position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 0.75rem 1rem;\n  text-decoration: none;\n  color: inherit;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s ease;\n  box-shadow: none !important;\n  transform: none !important;\n  border-radius: 0;\n  font-weight: inherit;\n  font-family: inherit;\n}\n.nav-dropdown-link:hover,\n.nav-toggle-level2:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-dropdown-link:active,\n.nav-toggle-level2:active {\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-dropdown-link:focus,\n.nav-toggle-level2:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: -2px;\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.nav-cta a {\n  padding: 16px 24px;\n  border-radius: 20rem;\n}\n\n.nav-mobile-toggle {\n  display: none;\n  align-items: center;\n  gap: 0.5rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  padding: 0.5rem;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n  margin-left: auto;\n  box-shadow: none !important;\n  transform: none !important;\n  font-size: inherit;\n  font-weight: inherit;\n  font-family: inherit;\n}\n.nav-mobile-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-mobile-toggle:active {\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-mobile-toggle:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.hamburger-icon {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n  width: 18px;\n  height: 14px;\n  order: 2;\n}\n\n.hamburger-line {\n  width: 100%;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n  font-size: 0.9rem;\n  font-weight: 500;\n  order: 1;\n}\n\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(1) {\n  opacity: 0;\n  transform: translateY(5px);\n}\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(2) {\n  transform: scaleX(1);\n}\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(3) {\n  opacity: 0;\n  transform: translateY(-5px);\n}\n\n.nav-mobile-content {\n  display: none;\n  flex-direction: column;\n  flex: 1;\n  width: 100%;\n  opacity: 0;\n  transform: translateY(20px);\n  transition: all 0.4s ease 0.2s;\n}\n.nav-mobile-expanded .nav-mobile-content {\n  display: flex;\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.nav-mobile-items {\n  flex: 1;\n  overflow-y: auto;\n}\n\n.nav-mobile-item {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 0 1rem;\n  opacity: 0;\n  transform: translateY(10px);\n  animation: none;\n}\n.nav-mobile-expanded .nav-mobile-item {\n  animation: fadeInUp 0.4s ease forwards;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(1) {\n  animation-delay: 0.3s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(2) {\n  animation-delay: 0.4s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(3) {\n  animation-delay: 0.5s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(4) {\n  animation-delay: 0.6s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(5) {\n  animation-delay: 0.7s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(6) {\n  animation-delay: 0.8s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(7) {\n  animation-delay: 0.9s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(8) {\n  animation-delay: 1s;\n}\n\n@keyframes fadeInUp {\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n  display: flex;\n  align-items: center;\n  width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n  flex: 1;\n  padding: 1rem 0;\n  text-decoration: none;\n  color: inherit;\n  font-size: 1.1rem;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.nav-mobile-item-header .nav-mobile-link:hover,\n.nav-mobile-subitem-header .nav-mobile-link:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n.nav-mobile-item-header .nav-mobile-link:focus,\n.nav-mobile-subitem-header .nav-mobile-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n  padding: 0 !important;\n  box-shadow: none !important;\n  transform: none !important;\n  font-size: inherit;\n  font-weight: inherit;\n  font-family: inherit;\n}\n.nav-mobile-toggle-item:hover,\n.nav-mobile-toggle-subitem:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-mobile-toggle-item:active,\n.nav-mobile-toggle-subitem:active {\n  transform: none !important;\n  box-shadow: none !important;\n}\n.nav-mobile-toggle-item:focus,\n.nav-mobile-toggle-subitem:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-toggle-item .nav-arrow,\n.nav-mobile-toggle-subitem .nav-arrow {\n  transition: all 0.3s ease;\n  opacity: 1;\n}\n.nav-mobile-toggle-item[aria-expanded=true] .nav-arrow,\n.nav-mobile-toggle-subitem[aria-expanded=true] .nav-arrow {\n  opacity: 0;\n  transform: scale(0.8);\n}\n.nav-mobile-toggle-item[aria-expanded=true]::after,\n.nav-mobile-toggle-subitem[aria-expanded=true]::after {\n  content: \"\";\n  position: absolute;\n  width: 12px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 1;\n  transform: scale(1);\n  transition: all 0.3s ease 0.1s;\n}\n.nav-mobile-toggle-item::after,\n.nav-mobile-toggle-subitem::after {\n  content: \"\";\n  position: absolute;\n  width: 12px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all 0.3s ease;\n}\n\n.nav-mobile-link {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: 1rem 0;\n  text-decoration: none;\n  color: inherit;\n  font-size: 1.1rem;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.nav-mobile-link:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n.nav-mobile-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-link-level1 {\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n  padding-left: 1rem;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n  padding-left: 2rem;\n  font-size: 0.9rem;\n  font-weight: 400;\n}\n\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n  max-height: 0;\n  overflow: hidden;\n  transition: max-height 0.3s ease;\n}\n.nav-mobile-submenu.nav-mobile-submenu-active,\n.nav-mobile-subsubmenu.nav-mobile-submenu-active {\n  max-height: 500px;\n}\n\n.nav-mobile-subitem {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.nav-mobile-cta {\n  margin-top: auto;\n  padding: 1rem;\n  width: 100%;\n  opacity: 0;\n  text-align: center;\n  transform: translateY(20px);\n  animation: none;\n}\n.nav-mobile-expanded .nav-mobile-cta {\n  animation: fadeInUp 0.4s ease 1.1s forwards;\n}\n\n.nav-mobile-cta-button {\n  padding: 16px 24px;\n  width: 100% !important;\n  border-radius: 20rem;\n  text-decoration: none;\n}\n\n@media (max-width: 1600px) {\n  .navbar-container {\n    padding: 0 20px;\n  }\n}\n@media (max-width: 1200px) {\n  .nav-cta {\n    display: none;\n  }\n}\n@media (max-width: 1024px) {\n  .nav-desktop {\n    display: none;\n  }\n  .nav-mobile-toggle {\n    display: flex;\n  }\n  .navbar {\n    justify-content: space-between;\n  }\n  .navbar .nav-logo {\n    flex: 0 0 auto;\n  }\n  .navbar.nav-mobile-expanded {\n    position: fixed;\n    top: 20px;\n    left: 20px;\n    right: 20px;\n    bottom: 20px;\n    max-width: none;\n    width: auto;\n    height: auto;\n    z-index: 1000;\n  }\n  .navbar-container.nav-expanded {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    padding: 20px;\n    z-index: 1000;\n  }\n}\n@media (max-width: 768px) {\n  .navbar-container {\n    padding: 0 20px;\n  }\n  nav {\n    padding: 12px;\n  }\n}\n@media (min-width: 769px) {\n  .nav-item:hover .nav-dropdown {\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n  }\n  .nav-item .nav-dropdown-item:hover .nav-dropdown-level2 {\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n  }\n}\n.nav-dropdown {\n  animation-duration: 0.2s;\n  animation-timing-function: ease-out;\n  animation-fill-mode: both;\n}\n.nav-dropdown.nav-dropdown-level2 {\n  animation-delay: 0.1s;\n}\n\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.8);\n  outline-offset: 2px;\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n.hero {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.hero-content {\n  height: 90vh;\n  min-height: 600px;\n  max-width: 1440px;\n  width: 100%;\n  margin: 8px;\n  background-size: cover;\n  background-position: center center !important;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-end;\n  align-items: flex-start;\n  position: relative;\n  overflow: hidden;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449);\n  background-blend-mode: normal, normal, color, normal, normal;\n  border-radius: 24px;\n  padding: 2rem;\n}\n\n.banner {\n  width: 50vw;\n  padding: 2rem;\n}\n\n.hero-cta {\n  margin-top: 2rem;\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n\n@media (max-width: 768px) {\n  .banner {\n    width: 100%;\n    padding: 0;\n  }\n}\n/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */\n.highlighted-updates {\n  display: grid;\n  gap: 2rem;\n  margin-bottom: 3rem;\n  /* Mobile: Stack vertically with calendar first */\n  grid-template-columns: 1fr;\n  grid-template-areas: \"calendar\" \"news\";\n  /* Desktop: Side by side with 2:1 ratio */\n}\n@media (min-width: 768px) {\n  .highlighted-updates {\n    grid-template-columns: 2fr 1fr;\n    grid-template-areas: \"news calendar\";\n  }\n}\n.highlighted-updates h2 {\n  grid-column: 1/-1;\n  margin-bottom: 1.5rem;\n}\n\n/* News Section - Featured article with overlay */\n.highlighted-updates-news {\n  grid-area: news;\n}\n.highlighted-updates-news article {\n  position: relative;\n  height: 100%;\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n.highlighted-updates-news article:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n.highlighted-updates-news article a {\n  display: block;\n  text-decoration: none;\n  color: inherit;\n  height: 100%;\n  /* Fallback for missing images */\n}\n.highlighted-updates-news article a > div {\n  position: relative;\n  min-height: 300px;\n  height: 100%;\n}\n@media (min-width: 768px) {\n  .highlighted-updates-news article a > div {\n    min-height: 400px;\n  }\n}\n.highlighted-updates-news article a img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n.highlighted-updates-news article a > div > div:first-child:not(.highlighted-news-content) {\n  background: #016449;\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n/* News content overlay */\n.highlighted-news-content {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  color: white;\n  padding: 2rem;\n}\n.highlighted-news-content time {\n  display: block;\n  font-size: 0.875rem;\n  opacity: 0.9;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n}\n.highlighted-news-content h2 {\n  margin: 0;\n  font-size: 1.5rem;\n  line-height: 1.3;\n}\n@media (min-width: 768px) {\n  .highlighted-news-content h2 {\n    font-size: 1.875rem;\n  }\n}\n.highlighted-news-content p {\n  margin: 0.75rem 0 0 0;\n  opacity: 0.9;\n  line-height: 1.5;\n}\n\n/* Calendar Section - Stacked event layout */\n.highlighted-updates-calendar {\n  grid-area: calendar;\n  /* Events container - compact single container */\n  /* Individual event items - compact layout */\n  /* Date component with card-stack effect for multiple events */\n  /* Event details */\n  /* \"See full calendar\" link - use secondary button */\n}\n.highlighted-updates-calendar .calendar-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n.highlighted-updates-calendar .calendar-header h3 {\n  margin: 0;\n  color: #016449;\n}\n.highlighted-updates-calendar .calendar-header a {\n  color: #9C2B32;\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.875rem;\n}\n.highlighted-updates-calendar .calendar-header a:hover {\n  text-decoration: underline;\n}\n.highlighted-updates-calendar .calendar-events {\n  background: white;\n  border-radius: 12px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n}\n.highlighted-updates-calendar .calendar-event {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 0.75rem 0;\n  text-decoration: none;\n  color: inherit;\n  border-bottom: 1px solid #f0f0f0;\n  transition: all 0.2s ease;\n}\n.highlighted-updates-calendar .calendar-event:last-child {\n  border-bottom: none;\n}\n.highlighted-updates-calendar .calendar-event:hover {\n  background: rgba(1, 100, 73, 0.05);\n  margin: 0 -1rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  border-radius: 8px;\n}\n.highlighted-updates-calendar .event-date {\n  flex-shrink: 0;\n  width: 50px;\n  height: 50px;\n  background: #016449;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  position: relative;\n  /* Card stack effect for multiple events */\n}\n.highlighted-updates-calendar .event-date.multiple-events::before {\n  content: \"\";\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  right: 3px;\n  bottom: 3px;\n  background: rgba(1, 100, 73, 0.3);\n  border-radius: 8px;\n  z-index: -1;\n}\n.highlighted-updates-calendar .event-date.multiple-events::after {\n  content: \"\";\n  position: absolute;\n  top: -6px;\n  left: -6px;\n  right: 6px;\n  bottom: 6px;\n  background: rgba(1, 100, 73, 0.15);\n  border-radius: 8px;\n  z-index: -2;\n}\n.highlighted-updates-calendar .event-date span:first-child {\n  font-size: 1.125rem;\n  font-weight: 700;\n  line-height: 1;\n}\n.highlighted-updates-calendar .event-date span:last-child {\n  font-size: 0.625rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  opacity: 0.9;\n}\n.highlighted-updates-calendar .event-details {\n  flex: 1;\n  min-width: 0;\n}\n.highlighted-updates-calendar .event-details h4 {\n  margin: 0 0 0.125rem 0;\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #1a1a1a;\n  line-height: 1.3;\n  /* Truncate long titles */\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.highlighted-updates-calendar .event-details .event-meta {\n  display: flex;\n  gap: 0.5rem;\n}\n.highlighted-updates-calendar .event-details .event-meta span {\n  font-size: 0.75rem;\n  color: #666;\n}\n.highlighted-updates-calendar .event-details .event-meta span:first-child {\n  font-weight: 500;\n  color: #016449;\n}\n.highlighted-updates-calendar .calendar-footer {\n  text-align: center;\n  padding: 8px 0;\n}\n\n/* News Carousel - Mobile-First Implementation */\n.news-carousel {\n  padding: 3rem 0;\n  /* Header with navigation */\n  /* Navigation buttons - Ultra simple with SVG arrows */\n  /* Carousel wrapper - allows visible overflow for shadows/effects */\n  /* Scrollable container */\n  /* Card base styles */\n  /* Card image */\n  /* Card content */\n  /* Archive card */\n}\n.news-carousel .container {\n  max-width: 1440px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n.news-carousel .carousel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n}\n.news-carousel .carousel-header h3 {\n  margin: 0;\n  color: #016449;\n  font-size: 1.5rem;\n}\n.news-carousel .carousel-header .carousel-nav {\n  display: flex;\n  flex-direction: row;\n  gap: 0.5rem;\n}\n@media (max-width: 640px) {\n  .news-carousel .carousel-header .carousel-nav {\n    display: none; /* Hide on mobile, rely on touch scrolling */\n  }\n}\n.news-carousel .carousel-btn {\n  width: 44px;\n  height: 44px;\n  background: white;\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s ease;\n  /* Override global button styles */\n  padding: 0;\n  box-shadow: none;\n  transform: none;\n  outline: none;\n  /* SVG arrow icons */\n  /* Previous arrow (left) */\n  /* Next arrow (right) */\n}\n.news-carousel .carousel-btn:hover:not(:disabled) {\n  background-color: #cfe7cb;\n  color: #016449;\n  box-shadow: none;\n  transform: none;\n}\n.news-carousel .carousel-btn:active {\n  box-shadow: none;\n  transform: none;\n}\n.news-carousel .carousel-btn:focus {\n  box-shadow: none;\n  outline: none;\n}\n.news-carousel .carousel-btn:disabled {\n  opacity: 0.4;\n  cursor: not-allowed;\n  box-shadow: none;\n  transform: none;\n}\n.news-carousel .carousel-btn::before {\n  content: \"\";\n  width: 16px;\n  height: 16px;\n  background-color: #000;\n  transition: background-color 0.2s ease;\n}\n.news-carousel .carousel-btn:hover:not(:disabled)::before {\n  background-color: #016449;\n}\n.news-carousel .carousel-btn--prev::before {\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n.news-carousel .carousel-btn--next::before {\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n.news-carousel .carousel-wrapper {\n  position: relative;\n  overflow: visible; /* Allow content to overflow for shadows/effects */\n  padding: 1rem 0;\n}\n.news-carousel .carousel-scroll {\n  display: flex;\n  gap: 1.5rem;\n  overflow-x: auto;\n  /* Note: overflow-y is automatically set to hidden when overflow-x is auto */\n  scroll-behavior: smooth;\n  scroll-snap-type: x mandatory;\n  -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE/Edge */\n  padding: 1rem;\n  margin: -1rem;\n  /* Hide scrollbar in WebKit browsers */\n}\n.news-carousel .carousel-scroll::-webkit-scrollbar {\n  display: none;\n}\n@media (max-width: 640px) {\n  .news-carousel .carousel-scroll {\n    gap: 1rem;\n    padding: 1rem 0.5rem;\n    margin: -1rem -0.5rem;\n  }\n}\n.news-carousel .carousel-card {\n  flex: 0 0 320px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  scroll-snap-align: start;\n  transition: all 0.3s ease;\n}\n@media (max-width: 768px) {\n  .news-carousel .carousel-card {\n    flex: 0 0 280px;\n  }\n}\n@media (max-width: 480px) {\n  .news-carousel .carousel-card {\n    flex: 0 0 260px;\n  }\n}\n.news-carousel .carousel-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n}\n.news-carousel .carousel-card a {\n  display: block;\n  text-decoration: none;\n  color: inherit;\n  height: 100%;\n}\n.news-carousel .card-image {\n  height: 200px;\n  overflow: hidden;\n  background: #f8f9fa;\n  position: relative;\n}\n@media (max-width: 480px) {\n  .news-carousel .card-image {\n    height: 160px;\n  }\n}\n.news-carousel .card-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.news-carousel .card-image .card-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #016449;\n  color: white;\n}\n.news-carousel .card-image .card-placeholder svg {\n  opacity: 0.6;\n}\n.news-carousel .card-content {\n  padding: 1.25rem;\n}\n@media (max-width: 480px) {\n  .news-carousel .card-content {\n    padding: 1rem;\n  }\n}\n.news-carousel .card-content time {\n  display: block;\n  font-size: 0.75rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.news-carousel .card-content h4 {\n  margin: 0 0 0.75rem 0;\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.4;\n  color: #1a1a1a;\n  /* Limit to 2 lines */\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n.news-carousel .card-content p {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #666;\n  line-height: 1.5;\n  /* Limit to 3 lines */\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n.news-carousel .carousel-card--archive {\n  background: #9C2B32;\n  color: white;\n}\n.news-carousel .carousel-card--archive .archive-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 2rem 1.5rem;\n  height: 100%;\n  min-height: 300px;\n}\n@media (max-width: 480px) {\n  .news-carousel .carousel-card--archive .archive-content {\n    padding: 1.5rem 1rem;\n    min-height: 260px;\n  }\n}\n.news-carousel .carousel-card--archive .archive-icon {\n  margin-bottom: 1rem;\n  opacity: 0.9;\n}\n.news-carousel .carousel-card--archive .archive-icon svg {\n  width: 48px;\n  height: 48px;\n}\n.news-carousel .carousel-card--archive h4 {\n  margin: 0 0 0.75rem 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: white;\n}\n.news-carousel .carousel-card--archive p {\n  margin: 0 0 1rem 0;\n  font-size: 0.875rem;\n  opacity: 0.9;\n  line-height: 1.5;\n  color: white;\n}\n.news-carousel .carousel-card--archive .archive-cta {\n  font-size: 0.875rem;\n  font-weight: 500;\n  opacity: 0.8;\n  color: white;\n}", ".navbar-container {\n    max-width: 1440px;\n    margin: 0 auto;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    padding: 0 80px; // 80px padding on sides\n    position: fixed;\n    top: 30px;\n    left: 0;\n    right: 0;\n    z-index: 100;\n\n    // Smooth transitions for show/hide\n    transform: translateY(0);\n    transition: transform 0.4s ease-in-out;\n\n    // Hidden state (when scrolling down)\n    &.nav-hidden {\n        transform: translateY(-200%);\n    }\n\n    // Visible state (when scrolling up or at top)\n    &.nav-visible {\n        transform: translateY(0);\n    }\n}\n\n.navbar {\n    position: relative;\n    width: 100%; // Full width within container\n    max-width: 1250px;\n    display: flex;\n    align-items: center;\n    padding: 16px; // Internal padding for nav content\n    border-radius: 10rem;\n    color: white;\n    transition: all 0.4s ease;\n\n    // Fallback for unsupported browsers\n    background: rgba(1, 100, 73, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    // Mobile expanded state\n    &.nav-mobile-expanded {\n        border-radius: 1.5rem;\n        flex-direction: column;\n        align-items: stretch;\n        height: 100vh;\n        max-width: none;\n        width: 100%;\n        margin: 0;\n        padding: 0;\n        overflow: hidden;\n        transform-origin: center top; // Anchor animation to center top\n\n        // Create header layout when expanded\n        .nav-logo,\n        .nav-mobile-toggle {\n            position: absolute;\n            z-index: 10;\n        }\n\n        // Logo and toggle in header row when expanded\n        .nav-logo {\n            top: 16px;\n            left: 16px;\n        }\n\n        .nav-mobile-toggle {\n            top: 16px;\n            right: 16px;\n        }\n\n        // Mobile content takes full height with proper scrolling\n        .nav-mobile-content {\n            flex: 1;\n            display: flex;\n            flex-direction: column;\n            height: 100%;\n            padding: 4rem 1rem 1rem;\n            overflow-y: auto;\n        }\n\n        // Items container\n        .nav-mobile-items {\n            flex: 1;\n        }\n    }\n}\n\n// Enhanced frosted glass effect for supported browsers\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n    .navbar {\n        // Semi-transparent background with brand color tint (#016449)\n        background: rgba(1, 100, 73, 0.6);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        -webkit-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        -moz-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n        // Apply backdrop filter directly to nav\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n\n        // Enhanced backdrop filter for expanded mobile state\n        &.nav-mobile-expanded {\n            background: rgba(0, 0, 0, 0.6);\n            -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n            backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n        }\n    }\n}\n// Nav content positioning with proper flexbox layout\n\n\n.nav-logo {\n    // Logo at flex-start (default)\n    flex: 0 0 auto;\n    max-width: 40px;\n    max-height: 40px;\n}\n\n.nav-logo img {\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n}\n\n.nav-collection {\n    // Navigation items in center\n    display: flex;\n    align-items: center;\n    flex: 1 1 auto;\n    justify-content: center;\n}\n\n.navbar button {\n    box-shadow: none;\n}\n\n.nav-cta {\n    // CTA at flex-end\n    flex: 0 0 auto;\n}\n\n.nav-item {\n    position: relative;\n}\n\n// Base navigation links and buttons\n.nav-link,\n.nav-toggle {\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n    color: inherit;\n    padding: 0.5rem 1rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    // Override global button styles\n    box-shadow: none !important;\n    transform: none !important;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Arrow animation for dropdowns (chevron to minus)\n.nav-toggle,\n.nav-toggle-level2 {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 10px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s;\n            right: 0.5rem; // Position for desktop dropdowns\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 10px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n        right: 0.5rem; // Position for desktop dropdowns\n    }\n}\n\n// Dropdown containers\n.nav-dropdown {\n    position: absolute;\n    top: 100%;\n    left: 0;\n    min-width: 200px;\n    background: rgba(0, 0, 0, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 0.5rem;\n    padding: 0.5rem 0;\n    margin-top: 0.5rem;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateY(-10px);\n    transition: all 0.2s ease;\n    z-index: 1000;\n\n    // Backdrop filter for supported browsers\n    @supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    }\n\n    // Show dropdown when parent is active\n    .nav-item:hover &,\n    .nav-item.nav-active & {\n        opacity: 1;\n        visibility: visible;\n        transform: translateY(0);\n    }\n}\n\n// Level 2 dropdown positioning\n.nav-dropdown-level2 {\n    top: 0;\n    left: 100%;\n    margin-top: 0;\n    margin-left: 0.5rem;\n}\n\n// Dropdown items\n.nav-dropdown-item {\n    position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    padding: 0.75rem 1rem;\n    text-decoration: none;\n    color: inherit;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 0.9rem;\n    transition: background-color 0.2s ease;\n\n    // Override global button styles\n    box-shadow: none !important;\n    transform: none !important;\n    border-radius: 0;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: -2px;\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n}\n\n.nav-cta a {\n    @extend .button-menu;\n    padding: 16px 24px; // Keep the larger padding for nav\n    border-radius: 20rem;\n}\n\n// Mobile Menu Toggle Button\n.nav-mobile-toggle {\n    display: none;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    padding: 0.5rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n    margin-left: auto; // Push to the right side\n\n    // Override global button styles\n    box-shadow: none !important;\n    transform: none !important;\n    font-size: inherit;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.hamburger-icon {\n    display: flex;\n    flex-direction: column;\n    gap: 3px;\n    width: 18px;\n    height: 14px;\n    order: 2; // Place icon after text\n}\n\n.hamburger-line {\n    width: 100%;\n    height: 2px;\n    background-color: currentColor;\n    border-radius: 1px;\n    transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n    font-size: 0.9rem;\n    font-weight: 500;\n    order: 1; // Place text before icon\n}\n\n// Hamburger animation when menu is open (hamburger to minus)\n.nav-mobile-toggle[aria-expanded=\"true\"] {\n    .hamburger-line {\n        &:nth-child(1) {\n            opacity: 0;\n            transform: translateY(5px);\n        }\n        &:nth-child(2) {\n            // Middle line becomes the minus\n            transform: scaleX(1);\n        }\n        &:nth-child(3) {\n            opacity: 0;\n            transform: translateY(-5px);\n        }\n    }\n}\n\n// Mobile Menu Content (inside expanded nav)\n.nav-mobile-content {\n    display: none;\n    flex-direction: column;\n    flex: 1;\n    width: 100%;\n    opacity: 0;\n    transform: translateY(20px);\n    transition: all 0.4s ease 0.2s; // Delay for smooth entrance\n\n    // Show when nav is expanded\n    .nav-mobile-expanded & {\n        display: flex;\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.nav-mobile-items {\n    flex: 1;\n    overflow-y: auto;\n}\n\n// Mobile Menu Items\n.nav-mobile-item {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    padding: 0 1rem;\n    opacity: 0;\n    transform: translateY(10px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease forwards;\n\n        // Stagger animation for each item\n        &:nth-child(1) { animation-delay: 0.3s; }\n        &:nth-child(2) { animation-delay: 0.4s; }\n        &:nth-child(3) { animation-delay: 0.5s; }\n        &:nth-child(4) { animation-delay: 0.6s; }\n        &:nth-child(5) { animation-delay: 0.7s; }\n        &:nth-child(6) { animation-delay: 0.8s; }\n        &:nth-child(7) { animation-delay: 0.9s; }\n        &:nth-child(8) { animation-delay: 1.0s; }\n    }\n}\n\n@keyframes fadeInUp {\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n// Split header for items with submenus\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n    display: flex;\n    align-items: center;\n    width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n    flex: 1;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Toggle buttons for submenus\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 48px;\n    height: 48px;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    // Override global button styles\n    padding: 0 !important;\n    box-shadow: none !important;\n    transform: none !important;\n    font-size: inherit;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Icon swap animation: chevron to minus (active)\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 12px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s; // Slight delay for smooth transition\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 12px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n    }\n}\n\n\n// Regular mobile links (no submenus)\n.nav-mobile-link {\n    display: flex;\n    align-items: center;\n    width: 100%;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.nav-mobile-link-level1 {\n    font-size: 1.2rem;\n    font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n    padding-left: 1rem;\n    font-size: 1rem;\n    font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n    padding-left: 2rem;\n    font-size: 0.9rem;\n    font-weight: 400;\n}\n\n// Mobile Submenus\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n    max-height: 0;\n    overflow: hidden;\n    transition: max-height 0.3s ease;\n\n    &.nav-mobile-submenu-active {\n        max-height: 500px; // Adjust based on content\n    }\n}\n\n.nav-mobile-subitem {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n// Mobile CTA at bottom\n.nav-mobile-cta {\n    margin-top: auto;\n    padding: 1rem;\n    width: 100%;\n    opacity: 0;\n    text-align: center;\n    transform: translateY(20px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease 1.1s forwards; // Animate in last\n    }\n}\n\n.nav-mobile-cta-button {\n    @extend .button-menu;\n    @extend .button-full;\n    padding: 16px 24px; // Keep the larger padding for mobile CTA\n    width: 100% !important;\n    border-radius: 20rem;\n    text-decoration: none;\n}\n\n//Decrease padding after 1600px\n@media (max-width: 1600px) {\n    .navbar-container {\n        padding: 0 20px;\n    }\n}\n\n//Remove CTA after 1200px\n@media (max-width: 1200px) {\n    .nav-cta {\n        display: none;\n    }\n}\n\n// Mobile navigation breakpoint\n@media (max-width: 1024px) {\n    .nav-desktop {\n        display: none;\n    }\n\n    .nav-mobile-toggle {\n        display: flex;\n    }\n\n    // Ensure proper flex layout in mobile mode\n    .navbar {\n        justify-content: space-between;\n\n        .nav-logo {\n            flex: 0 0 auto;\n        }\n\n        // When expanded, take full viewport\n        &.nav-mobile-expanded {\n            position: fixed;\n            top: 20px;\n            left: 20px;\n            right: 20px;\n            bottom: 20px;\n            max-width: none;\n            width: auto;\n            height: auto;\n            z-index: 1000;\n        }\n    }\n\n    // Adjust container for expanded state\n    .navbar-container {\n        &.nav-expanded {\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            padding: 20px;\n            z-index: 1000;\n        }\n    }\n}\n\n@media (max-width: 768px) {\n    .navbar-container {\n        padding: 0 20px; // Reduced padding on mobile\n    }\n\n    nav {\n        padding: 12px; // Reduced nav padding on mobile\n    }\n}\n\n// Enhanced hover effects for desktop\n@media (min-width: 769px) {\n    .nav-item {\n        // Hover to show dropdown\n        &:hover .nav-dropdown {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n\n        // Nested hover for level 2\n        .nav-dropdown-item:hover .nav-dropdown-level2 {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n    }\n}\n\n// Animation improvements\n.nav-dropdown {\n    // Smooth entrance animation\n    animation-duration: 0.2s;\n    animation-timing-function: ease-out;\n    animation-fill-mode: both;\n\n    &.nav-dropdown-level2 {\n        animation-delay: 0.1s; // Slight delay for nested dropdowns\n    }\n}\n\n// Focus management for accessibility\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n    outline: 2px solid rgba(255, 255, 255, 0.8);\n    outline-offset: 2px;\n    background-color: rgba(255, 255, 255, 0.15);\n}", ".hero {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    \n}\n\n.hero-content {\n    height: 90vh;\n    min-height: 600px;\n    max-width: 1440px;\n    width: 100%;\n    margin: 8px;\n    background-size: cover;\n    background-position: center center !important;\n    display: flex;\n    flex-direction: column;\n    justify-content: flex-end;\n    align-items: flex-start;\n    position: relative;\n    overflow: hidden;\n    background-color: rgba(0, 0, 0, 0.5);\n    color: white;\n    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449);\n    background-blend-mode: normal, normal, color, normal, normal;\n    border-radius: 24px;\n    padding: 2rem;\n}\n\n.banner {\n    width: 50vw;\n    padding: 2rem;\n}\n\n.hero-cta {\n    margin-top: 2rem;\n    display: flex;\n    gap: 1rem;\n    flex-wrap: wrap;\n}\n\n@media (max-width: 768px) {\n    .banner {\n        width: 100%;\n        padding: 0;\n    }\n}\n\n", "/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */\n.highlighted-updates {\n    display: grid;\n    gap: 2rem;\n    margin-bottom: 3rem;\n\n    /* Mobile: Stack vertically with calendar first */\n    grid-template-columns: 1fr;\n    grid-template-areas:\n        \"calendar\"\n        \"news\";\n\n    /* Desktop: Side by side with 2:1 ratio */\n    @media (min-width: 768px) {\n        grid-template-columns: 2fr 1fr;\n        grid-template-areas: \"news calendar\";\n    }\n\n    h2 {\n        grid-column: 1 / -1;\n        margin-bottom: 1.5rem;\n    }\n}\n\n/* News Section - Featured article with overlay */\n.highlighted-updates-news {\n    grid-area: news;\n\n    article {\n        position: relative;\n        height: 100%;\n        border-radius: 16px;\n        overflow: hidden;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n        transition: all 0.3s ease;\n\n        &:hover {\n            transform: translateY(-4px);\n            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n        }\n\n        a {\n            display: block;\n            text-decoration: none;\n            color: inherit;\n            height: 100%;\n\n            > div {\n                position: relative;\n                min-height: 300px;\n                height: 100%;\n\n                @media (min-width: 768px) {\n                    min-height: 400px;\n                }\n            }\n\n            img {\n                width: 100%;\n                height: 100%;\n                object-fit: cover;\n                position: absolute;\n                top: 0;\n                left: 0;\n            }\n\n            /* Fallback for missing images */\n            > div > div:first-child:not(.highlighted-news-content) {\n                background: #016449;\n                width: 100%;\n                height: 100%;\n                position: absolute;\n                top: 0;\n                left: 0;\n            }\n        }\n    }\n}\n\n/* News content overlay */\n.highlighted-news-content {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n    color: white;\n    padding: 2rem;\n\n    time {\n        display: block;\n        font-size: 0.875rem;\n        opacity: 0.9;\n        margin-bottom: 0.5rem;\n        font-weight: 500;\n    }\n\n    h2 {\n        margin: 0;\n        font-size: 1.5rem;\n        line-height: 1.3;\n\n        @media (min-width: 768px) {\n            font-size: 1.875rem;\n        }\n    }\n\n    p {\n        margin: 0.75rem 0 0 0;\n        opacity: 0.9;\n        line-height: 1.5;\n    }\n}\n\n/* Calendar Section - Stacked event layout */\n.highlighted-updates-calendar {\n    grid-area: calendar;\n\n    .calendar-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 1.5rem;\n\n        h3 {\n            margin: 0;\n            color: #016449;\n        }\n\n        a {\n            color: #9C2B32;\n            text-decoration: none;\n            font-weight: 500;\n            font-size: 0.875rem;\n\n            &:hover {\n                text-decoration: underline;\n            }\n        }\n    }\n\n    /* Events container - compact single container */\n    .calendar-events {\n        background: white;\n        border-radius: 12px;\n        padding: 1rem;\n        margin-bottom: 1rem;\n    }\n\n    /* Individual event items - compact layout */\n    .calendar-event {\n        display: flex;\n        align-items: center;\n        gap: 0.75rem;\n        padding: 0.75rem 0;\n        text-decoration: none;\n        color: inherit;\n        border-bottom: 1px solid #f0f0f0;\n        transition: all 0.2s ease;\n\n        &:last-child {\n            border-bottom: none;\n        }\n\n        &:hover {\n            background: rgba(1, 100, 73, 0.05);\n            margin: 0 -1rem;\n            padding-left: 1rem;\n            padding-right: 1rem;\n            border-radius: 8px;\n        }\n    }\n\n    /* Date component with card-stack effect for multiple events */\n    .event-date {\n        flex-shrink: 0;\n        width: 50px;\n        height: 50px;\n        background: #016449;\n        border-radius: 8px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: white;\n        position: relative;\n\n        /* Card stack effect for multiple events */\n        &.multiple-events {\n            &::before {\n                content: '';\n                position: absolute;\n                top: -3px;\n                left: -3px;\n                right: 3px;\n                bottom: 3px;\n                background: rgba(1, 100, 73, 0.3);\n                border-radius: 8px;\n                z-index: -1;\n            }\n\n            &::after {\n                content: '';\n                position: absolute;\n                top: -6px;\n                left: -6px;\n                right: 6px;\n                bottom: 6px;\n                background: rgba(1, 100, 73, 0.15);\n                border-radius: 8px;\n                z-index: -2;\n            }\n        }\n\n        span:first-child {\n            font-size: 1.125rem;\n            font-weight: 700;\n            line-height: 1;\n        }\n\n        span:last-child {\n            font-size: 0.625rem;\n            font-weight: 500;\n            text-transform: uppercase;\n            opacity: 0.9;\n        }\n    }\n\n    /* Event details */\n    .event-details {\n        flex: 1;\n        min-width: 0;\n\n        h4 {\n            margin: 0 0 0.125rem 0;\n            font-size: 0.875rem;\n            font-weight: 600;\n            color: #1a1a1a;\n            line-height: 1.3;\n\n            /* Truncate long titles */\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n        }\n\n        .event-meta {\n            display: flex;\n            gap: 0.5rem;\n\n            span {\n                font-size: 0.75rem;\n                color: #666;\n\n                &:first-child {\n                    font-weight: 500;\n                    color: #016449;\n                }\n            }\n        }\n    }\n\n    /* \"See full calendar\" link - use secondary button */\n    .calendar-footer {\n        text-align: center;\n        padding: 8px 0;\n    }\n}\n\n/* News Carousel - Mobile-First Implementation */\n.news-carousel {\n    padding: 3rem 0;\n\n    .container {\n        max-width: 1440px;\n        margin: 0 auto;\n        padding: 0 1rem;\n    }\n\n    /* Header with navigation */\n    .carousel-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 2rem;\n\n        h3 {\n            margin: 0;\n            color: #016449;\n            font-size: 1.5rem;\n        }\n\n        .carousel-nav {\n            display: flex;\n            flex-direction: row;\n            gap: 0.5rem;\n\n            @media (max-width: 640px) {\n                display: none; /* Hide on mobile, rely on touch scrolling */\n            }\n        }\n    }\n\n    /* Navigation buttons - Ultra simple with SVG arrows */\n    .carousel-btn {\n        width: 44px;\n        height: 44px;\n        background: white;\n        border: none;\n        border-radius: 50%;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: background-color 0.2s ease;\n\n        /* Override global button styles */\n        padding: 0;\n        box-shadow: none;\n        transform: none;\n        outline: none;\n\n        &:hover:not(:disabled) {\n            background-color: #cfe7cb;\n            color: #016449;\n            box-shadow: none;\n            transform: none;\n        }\n\n        &:active {\n            box-shadow: none;\n            transform: none;\n        }\n\n        &:focus {\n            box-shadow: none;\n            outline: none;\n        }\n\n        &:disabled {\n            opacity: 0.4;\n            cursor: not-allowed;\n            box-shadow: none;\n            transform: none;\n        }\n\n        /* SVG arrow icons */\n        &::before {\n            content: '';\n            width: 16px;\n            height: 16px;\n            background-color: #000;\n            transition: background-color 0.2s ease;\n        }\n\n        &:hover:not(:disabled)::before {\n            background-color: #016449;\n        }\n\n        /* Previous arrow (left) */\n        &--prev::before {\n            mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E\") no-repeat center;\n            mask-size: contain;\n        }\n\n        /* Next arrow (right) */\n        &--next::before {\n            mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z'/%3E%3C/svg%3E\") no-repeat center;\n            mask-size: contain;\n        }\n    }\n\n    /* Carousel wrapper - allows visible overflow for shadows/effects */\n    .carousel-wrapper {\n        position: relative;\n        overflow: visible; /* Allow content to overflow for shadows/effects */\n        padding: 1rem 0;\n    }\n\n    /* Scrollable container */\n    .carousel-scroll {\n        display: flex;\n        gap: 1.5rem;\n        overflow-x: auto;\n        /* Note: overflow-y is automatically set to hidden when overflow-x is auto */\n        scroll-behavior: smooth;\n        scroll-snap-type: x mandatory;\n        -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */\n        scrollbar-width: none; /* Firefox */\n        -ms-overflow-style: none; /* IE/Edge */\n        padding: 1rem;\n        margin: -1rem;\n\n        /* Hide scrollbar in WebKit browsers */\n        &::-webkit-scrollbar {\n            display: none;\n        }\n\n        @media (max-width: 640px) {\n            gap: 1rem;\n            padding: 1rem 0.5rem;\n            margin: -1rem -0.5rem;\n        }\n    }\n\n    /* Card base styles */\n    .carousel-card {\n        flex: 0 0 320px;\n        background: white;\n        border-radius: 8px;\n\n        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n        scroll-snap-align: start;\n        transition: all 0.3s ease;\n\n        @media (max-width: 768px) {\n            flex: 0 0 280px;\n        }\n\n        @media (max-width: 480px) {\n            flex: 0 0 260px;\n        }\n\n        &:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n        }\n\n        a {\n            display: block;\n            text-decoration: none;\n            color: inherit;\n            height: 100%;\n        }\n    }\n\n    /* Card image */\n    .card-image {\n        height: 200px;\n        overflow: hidden;\n        background: #f8f9fa;\n        position: relative;\n\n        @media (max-width: 480px) {\n            height: 160px;\n        }\n\n        img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n        }\n\n        .card-placeholder {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background: #016449;\n            color: white;\n\n            svg {\n                opacity: 0.6;\n            }\n        }\n    }\n\n    /* Card content */\n    .card-content {\n        padding: 1.25rem;\n\n        @media (max-width: 480px) {\n            padding: 1rem;\n        }\n\n        time {\n            display: block;\n            font-size: 0.75rem;\n            color: #666;\n            margin-bottom: 0.5rem;\n            font-weight: 500;\n            text-transform: uppercase;\n            letter-spacing: 0.5px;\n        }\n\n        h4 {\n            margin: 0 0 0.75rem 0;\n            font-size: 1rem;\n            font-weight: 600;\n            line-height: 1.4;\n            color: #1a1a1a;\n\n            /* Limit to 2 lines */\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n\n        p {\n            margin: 0;\n            font-size: 0.875rem;\n            color: #666;\n            line-height: 1.5;\n\n            /* Limit to 3 lines */\n            display: -webkit-box;\n            -webkit-line-clamp: 3;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n    }\n\n    /* Archive card */\n    .carousel-card--archive {\n        background: #9C2B32;\n        color: white;\n\n        .archive-content {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-content: center;\n            text-align: center;\n            padding: 2rem 1.5rem;\n            height: 100%;\n            min-height: 300px;\n\n            @media (max-width: 480px) {\n                padding: 1.5rem 1rem;\n                min-height: 260px;\n            }\n        }\n\n        .archive-icon {\n            margin-bottom: 1rem;\n            opacity: 0.9;\n\n            svg {\n                width: 48px;\n                height: 48px;\n            }\n        }\n\n        h4 {\n            margin: 0 0 0.75rem 0;\n            font-size: 1.25rem;\n            font-weight: 600;\n            color: white;\n        }\n\n        p {\n            margin: 0 0 1rem 0;\n            font-size: 0.875rem;\n            opacity: 0.9;\n            line-height: 1.5;\n            color: white;\n        }\n\n        .archive-cta {\n            font-size: 0.875rem;\n            font-weight: 500;\n            opacity: 0.8;\n            color: white;\n        }\n    }\n}"], "names": [], "sourceRoot": ""}