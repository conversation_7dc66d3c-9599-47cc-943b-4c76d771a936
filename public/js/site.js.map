{"version": 3, "file": "/js/site.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEO,SAASA,gBAAgB,GAAG;EACjC,IAAMC,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;EACvD,IAAMC,UAAU,GAAGF,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;EAC3D,IAAME,UAAU,GAAGH,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;EAE3D,IAAI,CAACF,KAAK,IAAI,CAACG,UAAU,IAAI,CAACC,UAAU,EAAE;EAE1C,IAAMC,KAAK,GAAGL,KAAK,CAACM,gBAAgB,CAAC,2BAA2B,CAAC;EACjE,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;EAExB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,UAAU,GAAG,CAAC;;EAElB;EACA,SAASC,mBAAmB,GAAG;IAC7B,IAAMC,cAAc,GAAGf,KAAK,CAACgB,aAAa,CAACC,WAAW;IACtD,IAAMC,SAAS,GAAGb,KAAK,CAAC,CAAC,CAAC;IAC1BI,SAAS,GAAGS,SAAS,CAACD,WAAW;IACjC,IAAME,GAAG,GAAGC,QAAQ,CAACC,gBAAgB,CAACrB,KAAK,CAAC,CAACmB,GAAG,CAAC,IAAI,EAAE;;IAEvD;IACAT,YAAY,GAAGY,IAAI,CAACC,KAAK,CAACR,cAAc,IAAIN,SAAS,GAAGU,GAAG,CAAC,CAAC;;IAE7D;IACA,IAAIT,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAG,CAAC;IAEtCc,aAAa,EAAE;EACjB;;EAEA;EACA,SAASA,aAAa,GAAG;IACvBrB,UAAU,CAACsB,QAAQ,GAAGjB,YAAY,IAAI,CAAC;IACvCJ,UAAU,CAACqB,QAAQ,GAAGjB,YAAY,IAAIH,KAAK,CAACE,MAAM,GAAGG,YAAY;EACnE;;EAEA;EACA,SAASgB,aAAa,CAACC,KAAK,EAAE;IAC5B,IAAMR,GAAG,GAAGC,QAAQ,CAACC,gBAAgB,CAACrB,KAAK,CAAC,CAACmB,GAAG,CAAC,IAAI,EAAE;IACvD,IAAMS,YAAY,GAAGD,KAAK,IAAIlB,SAAS,GAAGU,GAAG,CAAC;IAC9CnB,KAAK,CAAC6B,KAAK,CAACC,SAAS,yBAAkBF,YAAY,QAAK;IACxDpB,YAAY,GAAGmB,KAAK;IACpBH,aAAa,EAAE;EACjB;;EAEA;EACA,SAASO,YAAY,GAAG;IACtB,IAAIvB,YAAY,GAAG,CAAC,EAAE;MACpB;MACA,IAAMoB,YAAY,GAAGN,IAAI,CAACU,GAAG,CAACtB,YAAY,GAAG,CAAC,EAAEF,YAAY,CAAC;MAC7DkB,aAAa,CAACJ,IAAI,CAACW,GAAG,CAACzB,YAAY,GAAGoB,YAAY,EAAE,CAAC,CAAC,CAAC;IACzD;EACF;;EAEA;EACA,SAASM,QAAQ,GAAG;IAClB,IAAMC,QAAQ,GAAG9B,KAAK,CAACE,MAAM,GAAGG,YAAY;IAC5C,IAAIF,YAAY,GAAG2B,QAAQ,EAAE;MAC3B;MACA,IAAMC,cAAc,GAAG/B,KAAK,CAACE,MAAM,GAAGC,YAAY,GAAGE,YAAY;MACjE,IAAMkB,YAAY,GAAGQ,cAAc,GAAG1B,YAAY,GAAG,CAAC,GAAG0B,cAAc,GAAG1B,YAAY,GAAG,CAAC;MAC1FgB,aAAa,CAACJ,IAAI,CAACU,GAAG,CAACxB,YAAY,GAAGoB,YAAY,EAAEO,QAAQ,CAAC,CAAC;IAChE;EACF;;EAEA;EACA,SAASE,eAAe,CAACC,CAAC,EAAE;IAC1B3B,UAAU,GAAG,IAAI;IACjBX,KAAK,CAACuC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;IAE/B;IACA,IAAIF,CAAC,CAACG,IAAI,KAAK,WAAW,EAAE;MAC1B7B,MAAM,GAAG0B,CAAC,CAACI,OAAO;IACpB,CAAC,MAAM,IAAIJ,CAAC,CAACG,IAAI,KAAK,YAAY,EAAE;MAClC7B,MAAM,GAAG0B,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACD,OAAO;MAC7B;MACAJ,CAAC,CAACM,cAAc,EAAE;IACpB;IAEA/B,UAAU,GAAGL,YAAY,IAAIC,SAAS,GAAGW,QAAQ,CAACC,gBAAgB,CAACrB,KAAK,CAAC,CAACmB,GAAG,CAAC,IAAI,EAAE,CAAC;EACvF;EAEA,SAAS0B,cAAc,CAACP,CAAC,EAAE;IACzB,IAAI,CAAC3B,UAAU,EAAE;IAEjB,IAAImC,QAAQ;IACZ,IAAIR,CAAC,CAACG,IAAI,KAAK,WAAW,EAAE;MAC1BK,QAAQ,GAAGR,CAAC,CAACI,OAAO;IACtB,CAAC,MAAM,IAAIJ,CAAC,CAACG,IAAI,KAAK,WAAW,EAAE;MACjCK,QAAQ,GAAGR,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACD,OAAO;MAC/B;MACAJ,CAAC,CAACM,cAAc,EAAE;IACpB;IAEA,IAAMG,MAAM,GAAGnC,MAAM,GAAGkC,QAAQ;IAChC,IAAME,aAAa,GAAGnC,UAAU,GAAGkC,MAAM;;IAEzC;IACA/C,KAAK,CAAC6B,KAAK,CAACC,SAAS,yBAAkBkB,aAAa,QAAK;EAC3D;EAEA,SAASC,aAAa,CAACX,CAAC,EAAE;IACxB,IAAI,CAAC3B,UAAU,EAAE;IACjBA,UAAU,GAAG,KAAK;IAClBX,KAAK,CAACuC,SAAS,CAACW,MAAM,CAAC,UAAU,CAAC;IAElC,IAAIJ,QAAQ;IACZ,IAAIR,CAAC,CAACG,IAAI,KAAK,SAAS,EAAE;MACxBK,QAAQ,GAAGR,CAAC,CAACI,OAAO;IACtB,CAAC,MAAM,IAAIJ,CAAC,CAACG,IAAI,KAAK,UAAU,EAAE;MAChCK,QAAQ,GAAGR,CAAC,CAACa,cAAc,CAAC,CAAC,CAAC,CAACT,OAAO;IACxC;IAEA,IAAMK,MAAM,GAAGnC,MAAM,GAAGkC,QAAQ;;IAEhC;IACA,IAAMM,aAAa,GAAG3C,SAAS,GAAG,CAAC,CAAC,CAAC;;IAErC,IAAIa,IAAI,CAAC+B,GAAG,CAACN,MAAM,CAAC,GAAGK,aAAa,EAAE;MACpC,IAAIL,MAAM,GAAG,CAAC,IAAIvC,YAAY,GAAGH,KAAK,CAACE,MAAM,GAAGG,YAAY,EAAE;QAC5D;QACAwB,QAAQ,EAAE;MACZ,CAAC,MAAM,IAAIa,MAAM,GAAG,CAAC,IAAIvC,YAAY,GAAG,CAAC,EAAE;QACzC;QACAuB,YAAY,EAAE;MAChB,CAAC,MAAM;QACL;QACAL,aAAa,CAAClB,YAAY,CAAC;MAC7B;IACF,CAAC,MAAM;MACL;MACAkB,aAAa,CAAClB,YAAY,CAAC;IAC7B;EACF;;EAEA;EACA,SAAS8C,WAAW,CAAChB,CAAC,EAAE;IACtB,IAAIhB,IAAI,CAAC+B,GAAG,CAACf,CAAC,CAACS,MAAM,CAAC,GAAGzB,IAAI,CAAC+B,GAAG,CAACf,CAAC,CAACiB,MAAM,CAAC,EAAE;MAC3CjB,CAAC,CAACM,cAAc,EAAE;MAElB,IAAIN,CAAC,CAACS,MAAM,GAAG,CAAC,IAAIvC,YAAY,GAAGH,KAAK,CAACE,MAAM,GAAGG,YAAY,EAAE;QAC9DwB,QAAQ,EAAE;MACZ,CAAC,MAAM,IAAII,CAAC,CAACS,MAAM,GAAG,CAAC,IAAIvC,YAAY,GAAG,CAAC,EAAE;QAC3CuB,YAAY,EAAE;MAChB;IACF;EACF;;EAEA;EACA5B,UAAU,CAACqD,gBAAgB,CAAC,OAAO,EAAEzB,YAAY,CAAC;EAClD3B,UAAU,CAACoD,gBAAgB,CAAC,OAAO,EAAEtB,QAAQ,CAAC;;EAE9C;EACA,IAAI,EAAE,cAAc,IAAIuB,MAAM,CAAC,EAAE;IAC/BzD,KAAK,CAACwD,gBAAgB,CAAC,WAAW,EAAEnB,eAAe,CAAC;IACpDpC,QAAQ,CAACuD,gBAAgB,CAAC,WAAW,EAAEX,cAAc,CAAC;IACtD5C,QAAQ,CAACuD,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;EACrD;;EAEA;EACAjD,KAAK,CAACwD,gBAAgB,CAAC,YAAY,EAAEnB,eAAe,EAAE;IAAEqB,OAAO,EAAE;EAAM,CAAC,CAAC;EACzE1D,KAAK,CAACwD,gBAAgB,CAAC,WAAW,EAAEX,cAAc,EAAE;IAAEa,OAAO,EAAE;EAAM,CAAC,CAAC;EACvE1D,KAAK,CAACwD,gBAAgB,CAAC,UAAU,EAAEP,aAAa,EAAE;IAAES,OAAO,EAAE;EAAM,CAAC,CAAC;;EAErE;EACA1D,KAAK,CAACwD,gBAAgB,CAAC,OAAO,EAAEF,WAAW,EAAE;IAAEI,OAAO,EAAE;EAAM,CAAC,CAAC;;EAEhE;EACA1D,KAAK,CAACwD,gBAAgB,CAAC,aAAa,EAAE,UAAAlB,CAAC;IAAA,OAAIA,CAAC,CAACM,cAAc,EAAE;EAAA,EAAC;;EAE9D;EACA5C,KAAK,CAACwD,gBAAgB,CAAC,aAAa,EAAE,UAAAlB,CAAC;IAAA,OAAIA,CAAC,CAACM,cAAc,EAAE;EAAA,EAAC;;EAE9D;EACAa,MAAM,CAACD,gBAAgB,CAAC,QAAQ,EAAE,YAAM;IACtC1C,mBAAmB,EAAE;IACrBY,aAAa,CAACJ,IAAI,CAACU,GAAG,CAACxB,YAAY,EAAEH,KAAK,CAACE,MAAM,GAAGG,YAAY,CAAC,CAAC;EACpE,CAAC,CAAC;;EAEF;EACAI,mBAAmB,EAAE;EACrBY,aAAa,CAAC,CAAC,CAAC;AAClB;;;;;;;;;;;;;;;;AC9LA;AACA;AACA;AACA;;AAEA;AACO,SAASiC,oBAAoB,GAAG;EACrC,IAAMC,MAAM,GAAG3D,QAAQ,CAAC4D,aAAa,CAAC,mBAAmB,CAAC;EAC1D,IAAI,CAACD,MAAM,EAAE;EAEb,IAAIE,WAAW,GAAGL,MAAM,CAACM,OAAO;;EAEhC;EACA,SAASC,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAE;IAC7B,IAAIC,UAAU;IACd,OAAO,YAAW;MAChB,IAAMC,IAAI,GAAGC,SAAS;MACtB,IAAMC,OAAO,GAAG,IAAI;MACpB,IAAI,CAACH,UAAU,EAAE;QACfF,IAAI,CAACM,KAAK,CAACD,OAAO,EAAEF,IAAI,CAAC;QACzBD,UAAU,GAAG,IAAI;QACjBK,UAAU,CAAC;UAAA,OAAML,UAAU,GAAG,KAAK;QAAA,GAAED,KAAK,CAAC;MAC7C;IACF,CAAC;EACH;EAEA,SAASO,YAAY,GAAG;IACtB,IAAMC,cAAc,GAAGjB,MAAM,CAACM,OAAO;IACrC,IAAMY,gBAAgB,GAAGrD,IAAI,CAAC+B,GAAG,CAACqB,cAAc,GAAGZ,WAAW,CAAC;;IAE/D;IACA,IAAIa,gBAAgB,GAAG,CAAC,EAAE;IAE1B,IAAID,cAAc,IAAI,GAAG,EAAE;MACzB;MACAd,MAAM,CAACrB,SAAS,CAACW,MAAM,CAAC,YAAY,CAAC;MACrCU,MAAM,CAACrB,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;IACrC,CAAC,MAAM,IAAIkC,cAAc,GAAGZ,WAAW,IAAIY,cAAc,GAAG,GAAG,EAAE;MAC/D;MACAd,MAAM,CAACrB,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;MAClCoB,MAAM,CAACrB,SAAS,CAACW,MAAM,CAAC,aAAa,CAAC;IACxC,CAAC,MAAM,IAAIwB,cAAc,GAAGZ,WAAW,EAAE;MACvC;MACAF,MAAM,CAACrB,SAAS,CAACW,MAAM,CAAC,YAAY,CAAC;MACrCU,MAAM,CAACrB,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;IACrC;IAEAsB,WAAW,GAAGY,cAAc;EAC9B;;EAEA;EACA,IAAME,sBAAsB,GAAGZ,QAAQ,CAACS,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;;EAE3D;EACAhB,MAAM,CAACD,gBAAgB,CAAC,QAAQ,EAAEoB,sBAAsB,EAAE;IAAElB,OAAO,EAAE;EAAK,CAAC,CAAC;;EAE5E;EACAE,MAAM,CAACrB,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;AACrC;;AAEA;AACO,SAASqC,sBAAsB,GAAG;EACvC,IAAMC,QAAQ,GAAG7E,QAAQ,CAACK,gBAAgB,CAAC,WAAW,CAAC;EACvD,IAAI,CAACwE,QAAQ,CAACvE,MAAM,EAAE;;EAEtB;EACA,SAASwE,iBAAiB,CAACC,KAAK,EAAE;IAChCA,KAAK,CAACpC,cAAc,EAAE;IACtBoC,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAMC,MAAM,GAAGF,KAAK,CAACG,aAAa;IAClC,IAAMC,OAAO,GAAGF,MAAM,CAACG,OAAO,CAAC,+BAA+B,CAAC;IAC/D,IAAMC,UAAU,GAAGJ,MAAM,CAACK,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;;IAElE;IACA,IAAMC,WAAW,GAAGJ,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,QAAQ;IAC1E,IAAIG,WAAW,KAAK,QAAQ,EAAE;MAC5B;MACAvF,QAAQ,CAACK,gBAAgB,CAAC,WAAW,CAAC,CAACmF,OAAO,CAAC,UAAAC,IAAI,EAAI;QACrD,IAAIA,IAAI,KAAKN,OAAO,EAAE;UACpBO,aAAa,CAACD,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAME,cAAc,GAAGR,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC;MACvDO,cAAc,CAACtF,gBAAgB,CAAC,oBAAoB,CAAC,CAACmF,OAAO,CAAC,UAAAC,IAAI,EAAI;QACpE,IAAIA,IAAI,KAAKN,OAAO,EAAE;UACpBO,aAAa,CAACD,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIJ,UAAU,EAAE;MACdK,aAAa,CAACP,OAAO,CAAC;IACxB,CAAC,MAAM;MACLS,YAAY,CAACT,OAAO,CAAC;IACvB;EACF;;EAEA;EACA,SAASS,YAAY,CAACT,OAAO,EAAE;IAC7B,IAAMF,MAAM,GAAGE,OAAO,CAACvB,aAAa,CAAC,iCAAiC,CAAC;IACvE,IAAIqB,MAAM,EAAE;MACVA,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MAC5CV,OAAO,CAAC7C,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IACrC;EACF;;EAEA;EACA,SAASmD,aAAa,CAACP,OAAO,EAAE;IAC9B,IAAMF,MAAM,GAAGE,OAAO,CAACvB,aAAa,CAAC,iCAAiC,CAAC;IACvE,IAAIqB,MAAM,EAAE;MACVA,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MAC7CV,OAAO,CAAC7C,SAAS,CAACW,MAAM,CAAC,YAAY,CAAC;;MAEtC;MACAkC,OAAO,CAAC9E,gBAAgB,CAAC,oBAAoB,CAAC,CAACmF,OAAO,CAAC,UAAAM,SAAS,EAAI;QAClEJ,aAAa,CAACI,SAAS,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF;;EAEA;EACA,SAASC,iBAAiB,GAAG;IAC3BlB,QAAQ,CAACW,OAAO,CAAC,UAAAL,OAAO,EAAI;MAC1BO,aAAa,CAACP,OAAO,CAAC;IACxB,CAAC,CAAC;EACJ;;EAEA;EACA,SAASa,aAAa,CAACjB,KAAK,EAAE;IAC5B,IAAQkB,GAAG,GAAKlB,KAAK,CAAbkB,GAAG;IACX,IAAMC,aAAa,GAAGlG,QAAQ,CAACkG,aAAa;IAE5C,IAAID,GAAG,KAAK,QAAQ,EAAE;MACpBF,iBAAiB,EAAE;MACnB;MACA,IAAIG,aAAa,CAACd,OAAO,CAAC,eAAe,CAAC,EAAE;QAC1C,IAAMe,WAAW,GAAGnG,QAAQ,CAAC4D,aAAa,CAAC,aAAa,CAAC;QACzD,IAAIuC,WAAW,EAAEA,WAAW,CAACC,KAAK,EAAE;MACtC;MACA;IACF;;IAEA;IACA,IAAIF,aAAa,CAACd,OAAO,CAAC,eAAe,CAAC,EAAE;MAC1C,IAAMiB,QAAQ,GAAGH,aAAa,CAACd,OAAO,CAAC,eAAe,CAAC;MACvD,IAAMkB,iBAAiB,GAAGD,QAAQ,CAAChG,gBAAgB,CAAC,wCAAwC,CAAC;MAC7F,IAAME,YAAY,GAAGgG,KAAK,CAACC,IAAI,CAACF,iBAAiB,CAAC,CAACG,OAAO,CAACP,aAAa,CAAC;MAEzE,IAAID,GAAG,KAAK,WAAW,EAAE;QACvBlB,KAAK,CAACpC,cAAc,EAAE;QACtB,IAAM+D,SAAS,GAAG,CAACnG,YAAY,GAAG,CAAC,IAAI+F,iBAAiB,CAAChG,MAAM;QAC/DgG,iBAAiB,CAACI,SAAS,CAAC,CAACN,KAAK,EAAE;MACtC,CAAC,MAAM,IAAIH,GAAG,KAAK,SAAS,EAAE;QAC5BlB,KAAK,CAACpC,cAAc,EAAE;QACtB,IAAMgE,SAAS,GAAGpG,YAAY,KAAK,CAAC,GAAG+F,iBAAiB,CAAChG,MAAM,GAAG,CAAC,GAAGC,YAAY,GAAG,CAAC;QACtF+F,iBAAiB,CAACK,SAAS,CAAC,CAACP,KAAK,EAAE;MACtC;IACF;EACF;;EAEA;EACAvB,QAAQ,CAACW,OAAO,CAAC,UAAAL,OAAO,EAAI;IAC1B,IAAMF,MAAM,GAAGE,OAAO,CAACvB,aAAa,CAAC,iCAAiC,CAAC;IACvE,IAAIqB,MAAM,EAAE;MACVA,MAAM,CAAC1B,gBAAgB,CAAC,OAAO,EAAEuB,iBAAiB,CAAC;IACrD;EACF,CAAC,CAAC;;EAEF;EACA9E,QAAQ,CAACuD,gBAAgB,CAAC,OAAO,EAAE,UAACwB,KAAK,EAAK;IAC5C,IAAI,CAACA,KAAK,CAAC6B,MAAM,CAACxB,OAAO,CAAC,WAAW,CAAC,EAAE;MACtCW,iBAAiB,EAAE;IACrB;EACF,CAAC,CAAC;;EAEF;EACA/F,QAAQ,CAACuD,gBAAgB,CAAC,SAAS,EAAEyC,aAAa,CAAC;;EAEnD;EACAhG,QAAQ,CAACuD,gBAAgB,CAAC,UAAU,EAAE,YAAM;IAC1C;IACAgB,UAAU,CAAC,YAAM;MACf,IAAM2B,aAAa,GAAGlG,QAAQ,CAACkG,aAAa;MAC5C,IAAI,CAACA,aAAa,CAACd,OAAO,CAAC,iBAAiB,CAAC,EAAE;QAC7CW,iBAAiB,EAAE;MACrB;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,oBAAoB,GAAG;EACrC,IAAMC,YAAY,GAAG9G,QAAQ,CAAC4D,aAAa,CAAC,oBAAoB,CAAC;EACjE,IAAMmD,GAAG,GAAG/G,QAAQ,CAAC4D,aAAa,CAAC,KAAK,CAAC;EACzC,IAAMoD,eAAe,GAAGhH,QAAQ,CAAC4D,aAAa,CAAC,mBAAmB,CAAC;EACnE,IAAMqD,aAAa,GAAGjH,QAAQ,CAAC4D,aAAa,CAAC,qBAAqB,CAAC;EAEnE,IAAI,CAACkD,YAAY,IAAI,CAACC,GAAG,IAAI,CAACC,eAAe,IAAI,CAACC,aAAa,EAAE;;EAEjE;EACA,SAASC,gBAAgB,GAAG;IAC1B,IAAM7B,UAAU,GAAGyB,YAAY,CAACxB,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;IAExE,IAAID,UAAU,EAAE;MACd8B,eAAe,EAAE;IACnB,CAAC,MAAM;MACLC,cAAc,EAAE;IAClB;EACF;;EAEA;EACA,SAASA,cAAc,GAAG;IACxBL,GAAG,CAACzE,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACxCyE,eAAe,CAAC1E,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;IAC7CuE,YAAY,CAACjB,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;IAClDiB,YAAY,CAACjB,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC;;IAErD;IACA7F,QAAQ,CAACqH,IAAI,CAACzF,KAAK,CAAC0F,QAAQ,GAAG,QAAQ;;IAEvC;IACAC,qBAAqB,EAAE;;IAEvB;IACA;EACF;;EAEA;EACA,SAASJ,eAAe,GAAG;IACzBJ,GAAG,CAACzE,SAAS,CAACW,MAAM,CAAC,qBAAqB,CAAC;IAC3C+D,eAAe,CAAC1E,SAAS,CAACW,MAAM,CAAC,cAAc,CAAC;IAChD6D,YAAY,CAACjB,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;IACnDiB,YAAY,CAACjB,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC;;IAErD;IACA7F,QAAQ,CAACqH,IAAI,CAACzF,KAAK,CAAC0F,QAAQ,GAAG,EAAE;;IAEjC;IACA,IAAME,WAAW,GAAGP,aAAa,CAAC5G,gBAAgB,CAAC,uCAAuC,CAAC;IAC3FmH,WAAW,CAAChC,OAAO,CAAC,UAAAC,IAAI,EAAI;MAC1B,IAAMR,MAAM,GAAGQ,IAAI,CAAC7B,aAAa,CAAC,qDAAqD,CAAC;MACxF,IAAM6D,OAAO,GAAGhC,IAAI,CAAC7B,aAAa,CAAC,6CAA6C,CAAC;MACjF,IAAIqB,MAAM,IAAIwC,OAAO,EAAE;QACrBxC,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;QAC7C4B,OAAO,CAACnF,SAAS,CAACW,MAAM,CAAC,2BAA2B,CAAC;MACvD;IACF,CAAC,CAAC;;IAEF;IACA6D,YAAY,CAACV,KAAK,EAAE;EACtB;;EAEA;EACA,SAASmB,qBAAqB,GAAG;IAC/B,IAAMG,KAAK,GAAGT,aAAa,CAAC5G,gBAAgB,CAAC,mCAAmC,CAAC;IACjFqH,KAAK,CAAClC,OAAO,CAAC,UAAAC,IAAI,EAAI;MACpB;MACAA,IAAI,CAAC7D,KAAK,CAAC+F,OAAO,GAAG,GAAG;MACxBlC,IAAI,CAAC7D,KAAK,CAACC,SAAS,GAAG,kBAAkB;MACzC4D,IAAI,CAAC7D,KAAK,CAACgG,SAAS,GAAG,MAAM;MAC7BnC,IAAI,CAACoC,YAAY,CAAC,CAAC;MACnBpC,IAAI,CAAC7D,KAAK,CAACgG,SAAS,GAAG,IAAI;MAC3BnC,IAAI,CAAC7D,KAAK,CAAC+F,OAAO,GAAG,IAAI;MACzBlC,IAAI,CAAC7D,KAAK,CAACC,SAAS,GAAG,IAAI;IAC7B,CAAC,CAAC;EACJ;;EAIA;EACA,SAASiG,qBAAqB,GAAG;IAC/B;IACAX,eAAe,EAAE;EACnB;;EAEA;EACAL,YAAY,CAACvD,gBAAgB,CAAC,OAAO,EAAE2D,gBAAgB,CAAC;;EAExD;EACAD,aAAa,CAAC1D,gBAAgB,CAAC,OAAO,EAAE,UAACwB,KAAK,EAAK;IACjD,IAAMgD,YAAY,GAAGhD,KAAK,CAAC6B,MAAM,CAACxB,OAAO,CAAC,qDAAqD,CAAC;IAChG,IAAI2C,YAAY,EAAE;MAChBhD,KAAK,CAACpC,cAAc,EAAE;MACtBoC,KAAK,CAACC,eAAe,EAAE;MAEvB,IAAMgD,UAAU,GAAGD,YAAY,CAAC3C,OAAO,CAAC,uCAAuC,CAAC;MAChF,IAAMqC,OAAO,GAAGO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpE,aAAa,CAAC,6CAA6C,CAAC;MACxF,IAAMyB,UAAU,GAAG0C,YAAY,CAACzC,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MAExE,IAAImC,OAAO,EAAE;QACX,IAAIpC,UAAU,EAAE;UACd0C,YAAY,CAAClC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;UACnD4B,OAAO,CAACnF,SAAS,CAACW,MAAM,CAAC,2BAA2B,CAAC;QACvD,CAAC,MAAM;UACL8E,YAAY,CAAClC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;UAClD4B,OAAO,CAACnF,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACpD;MACF;IACF;EACF,CAAC,CAAC;;EAEF;EACA,IAAM0F,WAAW,GAAGjI,QAAQ,CAACK,gBAAgB,CAAC,0CAA0C,CAAC;EACzF4H,WAAW,CAACzC,OAAO,CAAC,UAAA0C,IAAI,EAAI;IAC1BA,IAAI,CAAC3E,gBAAgB,CAAC,OAAO,EAAEuE,qBAAqB,CAAC;EACvD,CAAC,CAAC;;EAEF;EACA9H,QAAQ,CAACuD,gBAAgB,CAAC,OAAO,EAAE,UAACwB,KAAK,EAAK;IAC5C,IAAIgC,GAAG,CAACzE,SAAS,CAAC6F,QAAQ,CAAC,qBAAqB,CAAC,IAC7C,CAACpB,GAAG,CAACoB,QAAQ,CAACpD,KAAK,CAAC6B,MAAM,CAAC,IAC3B,CAACE,YAAY,CAACqB,QAAQ,CAACpD,KAAK,CAAC6B,MAAM,CAAC,EAAE;MACxCO,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;;EAEF;EACAnH,QAAQ,CAACuD,gBAAgB,CAAC,SAAS,EAAE,UAACwB,KAAK,EAAK;IAC9C,IAAIA,KAAK,CAACkB,GAAG,KAAK,QAAQ,IAAIc,GAAG,CAACzE,SAAS,CAAC6F,QAAQ,CAAC,qBAAqB,CAAC,EAAE;MAC3EhB,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;;EAEF;EACA3D,MAAM,CAACD,gBAAgB,CAAC,QAAQ,EAAE,YAAM;IACtC,IAAIC,MAAM,CAAC4E,UAAU,GAAG,IAAI,IAAIrB,GAAG,CAACzE,SAAS,CAAC6F,QAAQ,CAAC,qBAAqB,CAAC,EAAE;MAC7EhB,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;AACJ;;;;;;;;;;;;;AC7UA;AACA;AACA;AACA;;AAEyG;AAC7C;;AAE5D;AACA,SAASkB,KAAK,CAACC,QAAQ,EAAE;EACvB,IAAItI,QAAQ,CAACuI,UAAU,KAAK,SAAS,EAAE;IACrCvI,QAAQ,CAACuD,gBAAgB,CAAC,kBAAkB,EAAE+E,QAAQ,CAAC;EACzD,CAAC,MAAM;IACLA,QAAQ,EAAE;EACZ;AACF;;AAEA;AACAD,KAAK,CAAC,YAAM;EACVG,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;;EAEpC;EACA/E,wEAAoB,EAAE;;EAEtB;EACAkB,0EAAsB,EAAE;;EAExB;EACAiC,wEAAoB,EAAE;;EAEtB;EACA/G,yEAAgB,EAAE;AACpB,CAAC,CAAC;;;;;;;;;;;AChCF;;;;;;;;;;;;ACAA;;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC3BA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;WCNA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UElDA;UACA;UACA;UACA;UACA;UACA;UACA", "sources": ["webpack:///./resources/js/components/carousel.js", "webpack:///./resources/js/components/nav.js", "webpack:///./resources/js/site.js", "webpack:///./resources/css/style.scss?df16", "webpack:///./resources/css/tailwind.css?f5c5", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/chunk loaded", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/make namespace object", "webpack:///webpack/runtime/jsonp chunk loading", "webpack:///webpack/before-startup", "webpack:///webpack/startup", "webpack:///webpack/after-startup"], "sourcesContent": ["/**\n * News Carousel Component\n * Handles horizontal scrolling, touch/drag interactions, and navigation buttons\n */\n\nexport function initNewsCarousel() {\n  const track = document.getElementById('carousel-track');\n  const prevButton = document.getElementById('carousel-prev');\n  const nextButton = document.getElementById('carousel-next');\n  \n  if (!track || !prevButton || !nextButton) return;\n\n  const cards = track.querySelectorAll('.news-card, .archive-card');\n  if (cards.length === 0) return;\n\n  let currentIndex = 0;\n  let cardWidth = 0;\n  let visibleCards = 0;\n  let isDragging = false;\n  let startX = 0;\n  let scrollLeft = 0;\n\n  // Calculate dimensions\n  function calculateDimensions() {\n    const containerWidth = track.parentElement.offsetWidth;\n    const firstCard = cards[0];\n    cardWidth = firstCard.offsetWidth;\n    const gap = parseInt(getComputedStyle(track).gap) || 24;\n    \n    // Calculate how many cards are visible\n    visibleCards = Math.floor(containerWidth / (cardWidth + gap));\n    \n    // Ensure at least 1 card is visible\n    if (visibleCards < 1) visibleCards = 1;\n    \n    updateButtons();\n  }\n\n  // Update button states\n  function updateButtons() {\n    prevButton.disabled = currentIndex <= 0;\n    nextButton.disabled = currentIndex >= cards.length - visibleCards;\n  }\n\n  // Scroll to specific index\n  function scrollToIndex(index) {\n    const gap = parseInt(getComputedStyle(track).gap) || 24;\n    const scrollAmount = index * (cardWidth + gap);\n    track.style.transform = `translateX(-${scrollAmount}px)`;\n    currentIndex = index;\n    updateButtons();\n  }\n\n  // Previous button handler\n  function goToPrevious() {\n    if (currentIndex > 0) {\n      // Calculate how many cards to scroll back (same logic as next)\n      const scrollAmount = Math.min(visibleCards - 1, currentIndex);\n      scrollToIndex(Math.max(currentIndex - scrollAmount, 0));\n    }\n  }\n\n  // Next button handler\n  function goToNext() {\n    const maxIndex = cards.length - visibleCards;\n    if (currentIndex < maxIndex) {\n      // Calculate how many cards to scroll\n      const remainingCards = cards.length - currentIndex - visibleCards;\n      const scrollAmount = remainingCards < visibleCards - 1 ? remainingCards : visibleCards - 1;\n      scrollToIndex(Math.min(currentIndex + scrollAmount, maxIndex));\n    }\n  }\n\n  // Touch/Mouse drag handlers\n  function handleDragStart(e) {\n    isDragging = true;\n    track.classList.add('dragging');\n\n    // Get the correct coordinates for both mouse and touch\n    if (e.type === 'mousedown') {\n      startX = e.clientX;\n    } else if (e.type === 'touchstart') {\n      startX = e.touches[0].clientX;\n      // Prevent scrolling on touch devices\n      e.preventDefault();\n    }\n\n    scrollLeft = currentIndex * (cardWidth + parseInt(getComputedStyle(track).gap) || 24);\n  }\n\n  function handleDragMove(e) {\n    if (!isDragging) return;\n\n    let currentX;\n    if (e.type === 'mousemove') {\n      currentX = e.clientX;\n    } else if (e.type === 'touchmove') {\n      currentX = e.touches[0].clientX;\n      // Prevent default scrolling behavior\n      e.preventDefault();\n    }\n\n    const deltaX = startX - currentX;\n    const newScrollLeft = scrollLeft + deltaX;\n\n    // Apply transform directly for smooth dragging\n    track.style.transform = `translateX(-${newScrollLeft}px)`;\n  }\n\n  function handleDragEnd(e) {\n    if (!isDragging) return;\n    isDragging = false;\n    track.classList.remove('dragging');\n\n    let currentX;\n    if (e.type === 'mouseup') {\n      currentX = e.clientX;\n    } else if (e.type === 'touchend') {\n      currentX = e.changedTouches[0].clientX;\n    }\n\n    const deltaX = startX - currentX;\n\n    // Calculate new index based on drag distance\n    const dragThreshold = cardWidth / 3; // 1/3 of card width to trigger scroll\n\n    if (Math.abs(deltaX) > dragThreshold) {\n      if (deltaX > 0 && currentIndex < cards.length - visibleCards) {\n        // Dragged left, go to next\n        goToNext();\n      } else if (deltaX < 0 && currentIndex > 0) {\n        // Dragged right, go to previous\n        goToPrevious();\n      } else {\n        // Snap back to current position\n        scrollToIndex(currentIndex);\n      }\n    } else {\n      // Snap back to current position\n      scrollToIndex(currentIndex);\n    }\n  }\n\n  // Horizontal scroll handler\n  function handleWheel(e) {\n    if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {\n      e.preventDefault();\n      \n      if (e.deltaX > 0 && currentIndex < cards.length - visibleCards) {\n        goToNext();\n      } else if (e.deltaX < 0 && currentIndex > 0) {\n        goToPrevious();\n      }\n    }\n  }\n\n  // Event listeners\n  prevButton.addEventListener('click', goToPrevious);\n  nextButton.addEventListener('click', goToNext);\n\n  // Mouse events (desktop only)\n  if (!('ontouchstart' in window)) {\n    track.addEventListener('mousedown', handleDragStart);\n    document.addEventListener('mousemove', handleDragMove);\n    document.addEventListener('mouseup', handleDragEnd);\n  }\n\n  // Touch events (mobile)\n  track.addEventListener('touchstart', handleDragStart, { passive: false });\n  track.addEventListener('touchmove', handleDragMove, { passive: false });\n  track.addEventListener('touchend', handleDragEnd, { passive: false });\n\n  // Wheel event for horizontal scrolling (desktop only)\n  track.addEventListener('wheel', handleWheel, { passive: false });\n\n  // Prevent context menu on long press\n  track.addEventListener('contextmenu', e => e.preventDefault());\n\n  // Prevent text selection during drag\n  track.addEventListener('selectstart', e => e.preventDefault());\n\n  // Resize handler\n  window.addEventListener('resize', () => {\n    calculateDimensions();\n    scrollToIndex(Math.min(currentIndex, cards.length - visibleCards));\n  });\n\n  // Initialize\n  calculateDimensions();\n  scrollToIndex(0);\n}\n", "/**\n * Navigation Component\n * Handles scroll-based navigation behavior and dropdown interactions for OK Tyr website\n */\n\n// Scroll Navigation Handler\nexport function initScrollNavigation() {\n  const navbar = document.querySelector('.navbar-container');\n  if (!navbar) return;\n\n  let lastScrollY = window.scrollY;\n\n  // Throttle scroll events for better performance\n  function throttle(func, limit) {\n    let inThrottle;\n    return function() {\n      const args = arguments;\n      const context = this;\n      if (!inThrottle) {\n        func.apply(context, args);\n        inThrottle = true;\n        setTimeout(() => inThrottle = false, limit);\n      }\n    }\n  }\n\n  function handleScroll() {\n    const currentScrollY = window.scrollY;\n    const scrollDifference = Math.abs(currentScrollY - lastScrollY);\n\n    // Only react to significant scroll movements (avoid jitter)\n    if (scrollDifference < 5) return;\n\n    if (currentScrollY <= 100) {\n      // Always show nav when near top of page\n      navbar.classList.remove('nav-hidden');\n      navbar.classList.add('nav-visible');\n    } else if (currentScrollY > lastScrollY && currentScrollY > 200) {\n      // Scrolling down - hide nav\n      navbar.classList.add('nav-hidden');\n      navbar.classList.remove('nav-visible');\n    } else if (currentScrollY < lastScrollY) {\n      // Scrolling up - show nav\n      navbar.classList.remove('nav-hidden');\n      navbar.classList.add('nav-visible');\n    }\n\n    lastScrollY = currentScrollY;\n  }\n\n  // Use throttled scroll handler\n  const throttledScrollHandler = throttle(handleScroll, 16); // ~60fps\n\n  // Add scroll listener\n  window.addEventListener('scroll', throttledScrollHandler, { passive: true });\n\n  // Initialize nav state\n  navbar.classList.add('nav-visible');\n}\n\n// Dropdown Navigation Handler\nexport function initDropdownNavigation() {\n  const navItems = document.querySelectorAll('.nav-item');\n  if (!navItems.length) return;\n\n  // Handle click events for dropdown toggles\n  function handleToggleClick(event) {\n    event.preventDefault();\n    event.stopPropagation();\n\n    const toggle = event.currentTarget;\n    const navItem = toggle.closest('.nav-item, .nav-dropdown-item');\n    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';\n\n    // Close all other dropdowns at the same level\n    const parentLevel = navItem.closest('.nav-dropdown') ? 'level2' : 'level1';\n    if (parentLevel === 'level1') {\n      // Close all level 1 dropdowns\n      document.querySelectorAll('.nav-item').forEach(item => {\n        if (item !== navItem) {\n          closeDropdown(item);\n        }\n      });\n    } else {\n      // Close all level 2 dropdowns in the same parent\n      const parentDropdown = navItem.closest('.nav-dropdown');\n      parentDropdown.querySelectorAll('.nav-dropdown-item').forEach(item => {\n        if (item !== navItem) {\n          closeDropdown(item);\n        }\n      });\n    }\n\n    // Toggle current dropdown\n    if (isExpanded) {\n      closeDropdown(navItem);\n    } else {\n      openDropdown(navItem);\n    }\n  }\n\n  // Open dropdown\n  function openDropdown(navItem) {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.setAttribute('aria-expanded', 'true');\n      navItem.classList.add('nav-active');\n    }\n  }\n\n  // Close dropdown\n  function closeDropdown(navItem) {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.setAttribute('aria-expanded', 'false');\n      navItem.classList.remove('nav-active');\n\n      // Also close any child dropdowns\n      navItem.querySelectorAll('.nav-dropdown-item').forEach(childItem => {\n        closeDropdown(childItem);\n      });\n    }\n  }\n\n  // Close all dropdowns\n  function closeAllDropdowns() {\n    navItems.forEach(navItem => {\n      closeDropdown(navItem);\n    });\n  }\n\n  // Handle keyboard navigation\n  function handleKeyDown(event) {\n    const { key } = event;\n    const activeElement = document.activeElement;\n\n    if (key === 'Escape') {\n      closeAllDropdowns();\n      // Focus the first nav toggle if we were in a dropdown\n      if (activeElement.closest('.nav-dropdown')) {\n        const firstToggle = document.querySelector('.nav-toggle');\n        if (firstToggle) firstToggle.focus();\n      }\n      return;\n    }\n\n    // Arrow key navigation within dropdowns\n    if (activeElement.closest('.nav-dropdown')) {\n      const dropdown = activeElement.closest('.nav-dropdown');\n      const focusableElements = dropdown.querySelectorAll('.nav-dropdown-link, .nav-toggle-level2');\n      const currentIndex = Array.from(focusableElements).indexOf(activeElement);\n\n      if (key === 'ArrowDown') {\n        event.preventDefault();\n        const nextIndex = (currentIndex + 1) % focusableElements.length;\n        focusableElements[nextIndex].focus();\n      } else if (key === 'ArrowUp') {\n        event.preventDefault();\n        const prevIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;\n        focusableElements[prevIndex].focus();\n      }\n    }\n  }\n\n  // Add event listeners\n  navItems.forEach(navItem => {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.addEventListener('click', handleToggleClick);\n    }\n  });\n\n  // Close dropdowns when clicking outside\n  document.addEventListener('click', (event) => {\n    if (!event.target.closest('.nav-item')) {\n      closeAllDropdowns();\n    }\n  });\n\n  // Handle keyboard navigation\n  document.addEventListener('keydown', handleKeyDown);\n\n  // Handle focus loss (for accessibility)\n  document.addEventListener('focusout', () => {\n    // Small delay to check if focus moved to another nav element\n    setTimeout(() => {\n      const activeElement = document.activeElement;\n      if (!activeElement.closest('.nav-collection')) {\n        closeAllDropdowns();\n      }\n    }, 100);\n  });\n}\n\n// Mobile Navigation Handler\nexport function initMobileNavigation() {\n  const mobileToggle = document.querySelector('.nav-mobile-toggle');\n  const nav = document.querySelector('nav');\n  const navbarContainer = document.querySelector('.navbar-container');\n  const mobileContent = document.querySelector('.nav-mobile-content');\n\n  if (!mobileToggle || !nav || !navbarContainer || !mobileContent) return;\n\n  // Toggle mobile menu\n  function toggleMobileMenu() {\n    const isExpanded = mobileToggle.getAttribute('aria-expanded') === 'true';\n\n    if (isExpanded) {\n      closeMobileMenu();\n    } else {\n      openMobileMenu();\n    }\n  }\n\n  // Open mobile menu (expand navbar)\n  function openMobileMenu() {\n    nav.classList.add('nav-mobile-expanded');\n    navbarContainer.classList.add('nav-expanded');\n    mobileToggle.setAttribute('aria-expanded', 'true');\n    mobileToggle.setAttribute('aria-label', 'Stäng meny');\n\n    // Prevent body scroll\n    document.body.style.overflow = 'hidden';\n\n    // Reset animations for menu items\n    resetMobileAnimations();\n\n    // Don't auto-focus - let users tab to first item naturally\n    // This prevents the visual focus highlight from appearing immediately\n  }\n\n  // Close mobile menu (collapse navbar)\n  function closeMobileMenu() {\n    nav.classList.remove('nav-mobile-expanded');\n    navbarContainer.classList.remove('nav-expanded');\n    mobileToggle.setAttribute('aria-expanded', 'false');\n    mobileToggle.setAttribute('aria-label', 'Öppna meny');\n\n    // Restore body scroll\n    document.body.style.overflow = '';\n\n    // Close all submenus\n    const mobileItems = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-subitem');\n    mobileItems.forEach(item => {\n      const toggle = item.querySelector('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');\n      const submenu = item.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');\n      if (toggle && submenu) {\n        toggle.setAttribute('aria-expanded', 'false');\n        submenu.classList.remove('nav-mobile-submenu-active');\n      }\n    });\n\n    // Return focus to toggle button\n    mobileToggle.focus();\n  }\n\n  // Reset mobile menu animations\n  function resetMobileAnimations() {\n    const items = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-cta');\n    items.forEach(item => {\n      // Reset to initial state\n      item.style.opacity = '0';\n      item.style.transform = 'translateY(10px)';\n      item.style.animation = 'none';\n      item.offsetHeight; // Trigger reflow\n      item.style.animation = null;\n      item.style.opacity = null;\n      item.style.transform = null;\n    });\n  }\n\n\n\n  // Handle mobile menu link clicks\n  function handleMobileLinkClick() {\n    // Close mobile menu when a link is clicked\n    closeMobileMenu();\n  }\n\n  // Event listeners\n  mobileToggle.addEventListener('click', toggleMobileMenu);\n\n  // Add submenu toggle listeners (using event delegation for dynamic content)\n  mobileContent.addEventListener('click', (event) => {\n    const toggleButton = event.target.closest('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');\n    if (toggleButton) {\n      event.preventDefault();\n      event.stopPropagation();\n\n      const parentItem = toggleButton.closest('.nav-mobile-item, .nav-mobile-subitem');\n      const submenu = parentItem?.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');\n      const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';\n\n      if (submenu) {\n        if (isExpanded) {\n          toggleButton.setAttribute('aria-expanded', 'false');\n          submenu.classList.remove('nav-mobile-submenu-active');\n        } else {\n          toggleButton.setAttribute('aria-expanded', 'true');\n          submenu.classList.add('nav-mobile-submenu-active');\n        }\n      }\n    }\n  });\n\n  // Add link click listeners to close menu\n  const mobileLinks = document.querySelectorAll('.nav-mobile-link, .nav-mobile-cta-button');\n  mobileLinks.forEach(link => {\n    link.addEventListener('click', handleMobileLinkClick);\n  });\n\n  // Close menu when clicking outside content area\n  document.addEventListener('click', (event) => {\n    if (nav.classList.contains('nav-mobile-expanded') &&\n        !nav.contains(event.target) &&\n        !mobileToggle.contains(event.target)) {\n      closeMobileMenu();\n    }\n  });\n\n  // Handle escape key\n  document.addEventListener('keydown', (event) => {\n    if (event.key === 'Escape' && nav.classList.contains('nav-mobile-expanded')) {\n      closeMobileMenu();\n    }\n  });\n\n  // Handle window resize to close mobile menu on desktop\n  window.addEventListener('resize', () => {\n    if (window.innerWidth > 1024 && nav.classList.contains('nav-mobile-expanded')) {\n      closeMobileMenu();\n    }\n  });\n}", "/**\n * Main Site JavaScript\n * Minimal setup for OK Tyr website\n */\n\nimport { initScrollNavigation, initDropdownNavigation, initMobileNavigation } from './components/nav.js';\nimport { initNewsCarousel } from './components/carousel.js';\n\n// Simple DOM ready function\nfunction ready(callback) {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback);\n  } else {\n    callback();\n  }\n}\n\n// Initialize when DOM is ready\nready(() => {\n  console.log('OK Tyr website loaded');\n\n  // Initialize scroll navigation\n  initScrollNavigation();\n\n  // Initialize dropdown navigation\n  initDropdownNavigation();\n\n  // Initialize mobile navigation\n  initMobileNavigation();\n\n  // Initialize news carousel\n  initNewsCarousel();\n});\n", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"/js/site\": 0,\n\t\"css/tailwind\": 0,\n\t\"css/style\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunk\"] = self[\"webpackChunk\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\n__webpack_require__.O(undefined, [\"css/tailwind\",\"css/style\"], () => (__webpack_require__(\"./resources/js/site.js\")))\n__webpack_require__.O(undefined, [\"css/tailwind\",\"css/style\"], () => (__webpack_require__(\"./resources/css/style.scss\")))\nvar __webpack_exports__ = __webpack_require__.O(undefined, [\"css/tailwind\",\"css/style\"], () => (__webpack_require__(\"./resources/css/tailwind.css\")))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["initNewsCarousel", "track", "document", "getElementById", "prevButton", "nextButton", "cards", "querySelectorAll", "length", "currentIndex", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "isDragging", "startX", "scrollLeft", "calculateDimensions", "containerWidth", "parentElement", "offsetWidth", "firstCard", "gap", "parseInt", "getComputedStyle", "Math", "floor", "updateButtons", "disabled", "scrollToIndex", "index", "scrollAmount", "style", "transform", "goToPrevious", "min", "max", "goToNext", "maxIndex", "remainingCards", "handleDragStart", "e", "classList", "add", "type", "clientX", "touches", "preventDefault", "handleDragMove", "currentX", "deltaX", "newScrollLeft", "handleDragEnd", "remove", "changedTouches", "drag<PERSON><PERSON><PERSON><PERSON>", "abs", "handleWheel", "deltaY", "addEventListener", "window", "passive", "initScrollNavigation", "navbar", "querySelector", "lastScrollY", "scrollY", "throttle", "func", "limit", "inThrottle", "args", "arguments", "context", "apply", "setTimeout", "handleScroll", "currentScrollY", "scrollDifference", "throttledScrollHandler", "initDropdownNavigation", "navItems", "handleToggleClick", "event", "stopPropagation", "toggle", "currentTarget", "navItem", "closest", "isExpanded", "getAttribute", "parentLevel", "for<PERSON>ach", "item", "closeDropdown", "parentDropdown", "openDropdown", "setAttribute", "childItem", "closeAllDropdowns", "handleKeyDown", "key", "activeElement", "firstToggle", "focus", "dropdown", "focusableElements", "Array", "from", "indexOf", "nextIndex", "prevIndex", "target", "initMobileNavigation", "mobileToggle", "nav", "navbarContainer", "mobileContent", "toggleMobileMenu", "closeMobileMenu", "openMobileMenu", "body", "overflow", "resetMobileAnimations", "mobileItems", "submenu", "items", "opacity", "animation", "offsetHeight", "handleMobileLinkClick", "to<PERSON><PERSON><PERSON><PERSON>", "parentItem", "mobileLinks", "link", "contains", "innerWidth", "ready", "callback", "readyState", "console", "log"], "sourceRoot": ""}